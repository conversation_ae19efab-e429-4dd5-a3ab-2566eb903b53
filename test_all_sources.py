#!/usr/bin/env python3
"""
Comprehensive test script for all data sources
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from egx_predictor import EGXStockPredictor

def test_csv_upload():
    """Test CSV upload functionality"""
    print("🔍 Testing CSV upload...")
    
    try:
        predictor = EGXStockPredictor(data_source='csv')
        
        # Test with sample CSV file
        csv_file = 'sample_egx_data.csv'
        if not os.path.exists(csv_file):
            print(f"❌ Sample CSV file not found: {csv_file}")
            return False
        
        data = predictor.load_csv_data(csv_file)
        
        if data is not None and not data.empty:
            print(f"✅ CSV upload successful! {len(data)} records loaded")
            print(f"Date range: {data.index[0].date()} to {data.index[-1].date()}")
            print(f"Columns: {list(data.columns)}")
            return True
        else:
            print("❌ CSV upload failed: No data returned")
            return False
            
    except Exception as e:
        print(f"❌ CSV upload error: {e}")
        return False

def test_tradingview():
    """Test TradingView integration"""
    print("🔍 Testing TradingView integration...")
    
    try:
        predictor = EGXStockPredictor(data_source='tradingview')
        
        # Test with popular EGX stock
        symbol = 'ETEL'  # Egyptian Company for Mobile Services
        data = predictor.fetch_tradingview_data(symbol, days=50)
        
        if data is not None and not data.empty:
            print(f"✅ TradingView successful! {len(data)} records for {symbol}")
            print(f"Price range: {data['Close'].min():.2f} - {data['Close'].max():.2f} EGP")
            return True
        else:
            print("❌ TradingView failed: No data returned")
            return False
            
    except Exception as e:
        print(f"❌ TradingView error: {e}")
        return False
    
    finally:
        # Clean up
        if hasattr(predictor, 'scraper') and predictor.scraper:
            predictor.scraper.close_driver()

def test_yfinance():
    """Test Yahoo Finance fallback"""
    print("🔍 Testing Yahoo Finance fallback...")
    
    try:
        predictor = EGXStockPredictor(data_source='yfinance')
        
        # Test with a symbol that might work on Yahoo Finance
        symbol = 'AAPL'  # Apple as a test
        data = predictor.fetch_yfinance_data(symbol, period='3mo')
        
        if data is not None and not data.empty:
            print(f"✅ Yahoo Finance successful! {len(data)} records for {symbol}")
            print(f"Price range: {data['Close'].min():.2f} - {data['Close'].max():.2f} USD")
            return True
        else:
            print("❌ Yahoo Finance failed: No data returned")
            return False
            
    except Exception as e:
        print(f"❌ Yahoo Finance error: {e}")
        return False

def test_complete_analysis():
    """Test complete analysis pipeline"""
    print("🔍 Testing complete analysis pipeline...")
    
    try:
        predictor = EGXStockPredictor(data_source='csv')
        
        # Load sample data
        csv_file = 'sample_egx_data.csv'
        predictor.load_csv_data(csv_file)
        
        # Run complete analysis with minimal parameters
        result = predictor.run_complete_analysis(lookback_days=15, epochs=3)
        
        if result is not None:
            print(f"✅ Complete analysis successful!")
            print(f"Accuracy: {result['metrics']['accuracy']:.2f}%")
            print(f"RMSE: {result['metrics']['rmse']:.2f}")
            print(f"Future predictions: {len(result['future_predictions'])} days")
            return True
        else:
            print("❌ Complete analysis failed")
            return False
            
    except Exception as e:
        print(f"❌ Complete analysis error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 EGX Stock Predictor - Comprehensive Test Suite")
    print("=" * 50)
    
    tests = [
        ("CSV Upload", test_csv_upload),
        ("TradingView Integration", test_tradingview),
        ("Yahoo Finance Fallback", test_yfinance),
        ("Complete Analysis Pipeline", test_complete_analysis)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
        print()
    
    # Summary
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! EGX Stock Predictor is ready for production!")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
