# EGX Stock Predictor - AI-Powered Egyptian Exchange Analysis

## 🚀 Overview

The EGX Stock Predictor is an advanced AI-powered application specifically designed for analyzing and predicting Egyptian Exchange (EGX) stock prices. It uses LSTM (Long Short-Term Memory) neural networks to provide accurate price predictions based on historical data.

## ✨ Key Features

### 📊 **Multiple Data Sources**
1. **CSV File Upload** - Upload your own historical stock data
2. **TradingView Integration** - Fetch live market data directly from TradingView
3. **Yahoo Finance Fallback** - Alternative data source for comparison

### 🧠 **Advanced AI Technology**
- **LSTM Neural Networks** - Specialized for time series prediction
- **TensorFlow/Keras** - Industry-standard deep learning framework
- **Memory Optimization** - Efficient resource usage for stable performance
- **Real-time Processing** - Live data analysis and prediction

### 📈 **Comprehensive Analysis**
- **7-Day Future Predictions** - Forecast stock prices for the next week
- **Performance Metrics** - Accuracy, RMSE, MAE calculations
- **Interactive Charts** - Visual representation of predictions vs actual prices
- **EGX Currency Support** - Prices displayed in Egyptian Pounds (EGP)

## 🏗️ Project Structure

```
AI-Stock-Predictor/
├── app.py                 # Original US stocks app
├── app_egx.py            # Enhanced EGX stocks app
├── spp2.py               # Original predictor class
├── egx_predictor.py      # Enhanced EGX predictor class
├── scrapers/             # TradingView scraping utilities
│   ├── __init__.py
│   ├── price_scraper.py  # Advanced TradingView scraper
│   └── chromedriver.exe  # Chrome WebDriver
├── templates/
│   ├── index.html        # Original app template
│   ├── index_egx.html    # EGX app template
│   ├── about.html        # Original about page
│   └── about_egx.html    # EGX about page
├── uploads/              # Temporary CSV upload directory
├── venv/                 # Virtual environment
├── requirements.txt      # Python dependencies
├── sample_egx_data.csv   # Sample CSV data for testing
├── run_app.bat          # Launch original app (Windows)
├── run_egx_app.bat      # Launch EGX app (Windows)
├── run_app.ps1          # Launch original app (PowerShell)
└── run_egx_app.ps1      # Launch EGX app (PowerShell)
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8 or higher
- Chrome browser (for TradingView scraping)
- 4GB+ RAM recommended

### Step 1: Clone Repository
```bash
git clone https://github.com/theyrshetty/AI-Stock-Predictor.git
cd AI-Stock-Predictor
```

### Step 2: Create Virtual Environment
```bash
python -m venv venv
```

### Step 3: Activate Virtual Environment
**Windows (Command Prompt):**
```bash
venv\Scripts\activate
```

**Windows (PowerShell):**
```powershell
.\venv\Scripts\Activate.ps1
```

### Step 4: Install Dependencies
```bash
pip install -r requirements.txt
```

## 🚀 Running the Applications

### EGX Stock Predictor (Recommended)
```bash
# Method 1: Direct Python
python app_egx.py

# Method 2: Batch file (Windows)
run_egx_app.bat

# Method 3: PowerShell (Windows)
.\run_egx_app.ps1
```
**Access at:** http://localhost:5001

### Original US Stock Predictor
```bash
# Method 1: Direct Python
python app.py

# Method 2: Batch file (Windows)
run_app.bat

# Method 3: PowerShell (Windows)
.\run_app.ps1
```
**Access at:** http://localhost:5000

## 📋 How to Use

### 1. CSV File Upload
1. Select "CSV Upload" as data source
2. Click "Choose File" and select your CSV file
3. Ensure CSV has columns: Date, Open, High, Low, Close, Volume
4. Configure model parameters (Lookback Days, Epochs)
5. Click "Analyze & Predict"

### 2. TradingView Data
1. Select "TradingView" as data source
2. Enter EGX stock symbol (e.g., CIB, ETEL, SWDY)
3. Or choose from popular stocks dropdown
4. Configure model parameters
5. Click "Analyze & Predict"

### 3. Yahoo Finance Data
1. Select "Yahoo Finance" as data source
2. Enter stock symbol with .CA suffix (e.g., CIB.CA)
3. Configure model parameters
4. Click "Analyze & Predict"

## 📊 Supported EGX Stocks

### Banking & Financial
- **COMI** - Commercial International Bank
- **ALEX** - Alexandria Mineral Oils Company

### Telecommunications
- **ETEL** - Egyptian Company for Mobile Services

### Real Estate
- **PHDC** - Palm Hills Developments
- **SODIC** - Sixth of October Development
- **TALAAT** - Talaat Moustafa Group
- **MNHD** - Madinet Nasr Housing

### Industrial
- **SWDY** - El Sewedy Electric Company
- **HRHO** - Hassan Allam Holding
- **OCDI** - Orascom Construction
- **EAST** - Eastern Company

### Food & Beverages
- **JUFO** - Juhayna Food Industries
- **DOMTY** - Domty

### Chemicals
- **SIDI** - Sidi Kerir Petrochemicals
- **KIMA** - Abu Qir Fertilizers

## 📁 CSV File Format

Your CSV file should have the following columns:

```csv
Date,Open,High,Low,Close,Volume
2023-01-01,100.50,102.30,99.80,101.20,1500000
2023-01-02,101.20,103.50,100.90,102.80,1600000
...
```

**Column Requirements:**
- **Date**: YYYY-MM-DD format
- **Open**: Opening price
- **High**: Highest price of the day
- **Low**: Lowest price of the day
- **Close**: Closing price (required)
- **Volume**: Trading volume

**Notes:**
- Date and Close columns are mandatory
- Missing OHLV columns will be auto-filled
- Supports various encodings (UTF-8, Latin-1, etc.)
- Maximum file size: 16MB

## ⚙️ Model Parameters

### Lookback Days (10-60)
- Number of previous days used for prediction
- Higher values capture longer trends
- Lower values are more responsive to recent changes
- **Recommended**: 30 days

### Training Epochs (5-20)
- Number of training iterations
- Higher values may improve accuracy but increase training time
- Risk of overfitting with too many epochs
- **Recommended**: 10 epochs

## 📈 Performance Metrics

### Accuracy
Percentage of predictions within 5% of actual prices

### RMSE (Root Mean Square Error)
Measures prediction error magnitude

### MAE (Mean Absolute Error)
Average absolute difference between predicted and actual prices

## 🔧 Technical Specifications

- **Framework**: Flask (Python web framework)
- **AI Model**: LSTM Neural Networks
- **Deep Learning**: TensorFlow/Keras
- **Data Processing**: Pandas, NumPy
- **Visualization**: Matplotlib
- **Web Scraping**: Selenium, BeautifulSoup
- **Frontend**: Bootstrap 5, Font Awesome

## ⚠️ Important Disclaimer

This tool is for **educational and research purposes only**. Stock predictions are based on historical data and machine learning models, which cannot guarantee future performance.

**Always:**
- Consult with financial advisors
- Conduct your own research
- Consider multiple factors beyond technical analysis
- Remember that past performance does not indicate future results

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**: Ensure virtual environment is activated and dependencies installed
2. **Memory Issues**: Reduce lookback days or epochs
3. **CSV Upload Fails**: Check file format and column names
4. **TradingView Scraping**: Ensure Chrome browser is installed

### Getting Help

1. Check the console output for error messages
2. Verify your CSV file format matches requirements
3. Try with sample data first (`sample_egx_data.csv`)
4. Reduce model parameters if experiencing memory issues

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

---

**Built with ❤️ for the Egyptian Exchange community**
