from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
import os
from datetime import datetime
import traceback
import sys
import io
import gc
import psutil
import tensorflow as tf
from werkzeug.utils import secure_filename

# Configure TensorFlow to use CPU only and limit memory
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # Force CPU usage
tf.config.set_visible_devices([], 'GPU')  # Disable GPU

# Import the EGX StockPredictor class
from egx_predictor import EGXStockPredictor

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'dev-insecure-key')

# Configuration for file uploads
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'csv', 'txt'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB max file size

# Create upload directory if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_FILE_SIZE

# Popular EGX stocks for quick selection (correct symbols)
EGX_STOCKS = {
    'COMI': 'Commercial International Bank',
    'ETEL': 'Egyptian Company for Mobile Services',
    'HRHO': 'Hassan Allam Holding',
    'OCDI': 'Orascom Construction Industries',
    'SWDY': 'El Sewedy Electric Company',
    'TMGH': 'TMG Holding',
    'PHDC': 'Palm Hills Developments',
    'SODIC': 'Sixth of October Development & Investment Company',
    'TALAAT': 'Talaat Moustafa Group Holding',
    'EKHO': 'El Kahera Housing',
    'MNHD': 'Madinet Nasr Housing and Development',
    'AMER': 'Amer Group Holding',
    'JUFO': 'Juhayna Food Industries',
    'DOMTY': 'Domty',
    'SKPC': 'Skydive Egypt',
    'EAST': 'Eastern Company',
    'ALEX': 'Alexandria Mineral Oils Company',
    'SIDI': 'Sidi Kerir Petrochemicals',
    'KIMA': 'Abu Qir Fertilizers and Chemical Industries Company',
    'FEBE': 'Egyptian Iron & Steel Company'
}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def check_memory_usage():
    """Check current memory usage"""
    process = psutil.Process()
    memory_info = process.memory_info()
    memory_mb = memory_info.rss / 1024 / 1024
    return memory_mb

@app.route('/', methods=['GET', 'POST'])
def index():
    result = None

    if request.method == 'POST':
        predictor = None
        try:
            # Check initial memory
            initial_memory = check_memory_usage()
            print(f"Initial memory usage: {initial_memory:.2f} MB")

            # Get form data
            data_source = request.form.get('data_source', 'csv')
            lookback = min(int(request.form.get('lookback', 30)), 60)
            epochs = min(int(request.form.get('epochs', 10)), 20)

            # Validate inputs
            if not (10 <= lookback <= 60):
                flash('Lookback days must be between 10 and 60', 'error')
                return render_template("index_egx.html", egx_stocks=EGX_STOCKS)

            if not (5 <= epochs <= 20):
                flash('Epochs must be between 5 and 20', 'error')
                return render_template("index_egx.html", egx_stocks=EGX_STOCKS)

            # Initialize predictor
            predictor = EGXStockPredictor(data_source=data_source)

            # Handle different data sources
            if data_source == 'csv':
                # Handle CSV file upload
                if 'csv_file' not in request.files:
                    flash('No CSV file selected', 'error')
                    return render_template("index_egx.html", egx_stocks=EGX_STOCKS)

                file = request.files['csv_file']
                if file.filename == '':
                    flash('No CSV file selected', 'error')
                    return render_template("index_egx.html", egx_stocks=EGX_STOCKS)

                if file and allowed_file(file.filename):
                    filename = secure_filename(file.filename)
                    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    file.save(filepath)

                    # Load CSV data
                    predictor.load_csv_data(filepath)

                    # Clean up uploaded file
                    os.remove(filepath)
                else:
                    flash('Invalid file type. Please upload a CSV file.', 'error')
                    return render_template("index_egx.html", egx_stocks=EGX_STOCKS)

            elif data_source == 'tradingview':
                # Handle TradingView data
                symbol = request.form.get('symbol', '').upper().strip()
                if not symbol:
                    flash('Please enter a stock symbol for TradingView data', 'error')
                    return render_template("index_egx.html", egx_stocks=EGX_STOCKS)

                predictor.symbol = symbol
                predictor.fetch_tradingview_data(symbol)

            elif data_source == 'yfinance':
                # Handle Yahoo Finance data (fallback)
                symbol = request.form.get('symbol', '').upper().strip()
                if not symbol:
                    flash('Please enter a stock symbol for Yahoo Finance data', 'error')
                    return render_template("index_egx.html", egx_stocks=EGX_STOCKS)

                predictor.symbol = symbol
                predictor.fetch_yfinance_data(symbol + '.CA')  # Add Cairo exchange suffix

            # Check memory before training
            current_memory = check_memory_usage()
            if current_memory > 400:  # If using more than 400MB
                gc.collect()  # Force garbage collection
                print(f"Memory after GC: {check_memory_usage():.2f} MB")

            # Suppress console output during training
            old_stdout = sys.stdout
            sys.stdout = io.StringIO()

            try:
                # Run complete analysis
                analysis_result = predictor.run_complete_analysis(
                    lookback_days=lookback,
                    epochs=epochs
                )
            finally:
                sys.stdout = old_stdout

            if not analysis_result:
                flash(f'Analysis failed. Try with smaller parameters.', 'error')
                return render_template("index_egx.html", egx_stocks=EGX_STOCKS)

            # Prepare result data
            result = {
                'symbol': predictor.symbol or 'Uploaded Data',
                'data_source': data_source.title(),
                'accuracy': f"{analysis_result['metrics']['accuracy']:.2f}",
                'rmse': f"{analysis_result['metrics']['rmse']:.2f}",
                'mae': f"{analysis_result['metrics']['mae']:.2f}",
                'predictions': [
                    {
                        'date': analysis_result['future_dates'][i].strftime('%Y-%m-%d'),
                        'price': f"{analysis_result['future_predictions'][i][0]:.2f}"
                    } for i in range(min(7, len(analysis_result['future_predictions'])))
                ],
                'data_range': f"{predictor.data.index[0].strftime('%Y-%m-%d')} to {predictor.data.index[-1].strftime('%Y-%m-%d')}",
                'training_params': {
                    'lookback_days': lookback,
                    'epochs': epochs,
                    'data_points': len(predictor.data)
                },
                'plots': analysis_result['plots']
            }

            flash(f'Prediction completed successfully!', 'success')

            # Log memory usage
            final_memory = check_memory_usage()
            print(f"Final memory usage: {final_memory:.2f} MB")

        except Exception as e:
            error_msg = str(e)
            print(f"Error: {error_msg}")
            print(traceback.format_exc())

            # Check if it's a memory error
            if "memory" in error_msg.lower() or "killed" in error_msg.lower():
                flash('Prediction failed due to memory constraints. Try reducing lookback days or epochs.', 'error')
            else:
                flash(f'Error processing prediction: {error_msg}', 'error')
            result = None

        finally:
            # Clean up resources
            if predictor:
                del predictor
            gc.collect()  # Force garbage collection

            # Clear TensorFlow session
            try:
                tf.keras.backend.clear_session()
            except:
                pass

    return render_template("index_egx.html", result=result, egx_stocks=EGX_STOCKS)

@app.route('/about')
def about():
    """About page explaining the EGX model"""
    return render_template('about_egx.html')

@app.route('/health')
def health():
    """Health check endpoint"""
    memory_usage = check_memory_usage()
    return jsonify({
        'status': 'healthy',
        'memory_mb': memory_usage,
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    # Create uploads directory if it doesn't exist
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('static/plots', exist_ok=True)

    # Configure for production
    port = int(os.environ.get('PORT', 5000))  # Use port 5000 as requested
    debug = os.environ.get('FLASK_ENV') == 'development'

    print(f"Starting EGX Stock Predictor on port {port}, debug={debug}")
    print(f"TensorFlow version: {tf.__version__}")
    print(f"GPU available: {tf.config.list_physical_devices('GPU')}")

    app.run(debug=debug, host='0.0.0.0', port=port)
