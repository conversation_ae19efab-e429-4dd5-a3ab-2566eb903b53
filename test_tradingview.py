#!/usr/bin/env python3
"""
Test script for TradingView integration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from egx_predictor import EGXStockPredictor

def test_tradingview_integration():
    """Test the TradingView integration"""
    print("Testing TradingView integration...")

    try:
        # Initialize predictor
        predictor = EGXStockPredictor(data_source='tradingview')

        # Test with a popular EGX stock
        symbol = 'COMI'  # Commercial International Bank
        print(f"Testing with symbol: {symbol}")

        # Fetch TradingView data
        data = predictor.fetch_tradingview_data(symbol, days=100)

        if data is not None and not data.empty:
            print(f"✅ Success! Fetched {len(data)} data points")
            print(f"Date range: {data.index[0].date()} to {data.index[-1].date()}")
            print(f"Price range: {data['Close'].min():.2f} - {data['Close'].max():.2f} EGP")
            print(f"Sample data:")
            print(data.head())
            return True
        else:
            print("❌ Failed: No data returned")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # Clean up
        if hasattr(predictor, 'scraper') and predictor.scraper:
            predictor.scraper.close_driver()

if __name__ == "__main__":
    success = test_tradingview_integration()
    if success:
        print("\n🎉 TradingView integration test passed!")
    else:
        print("\n💥 TradingView integration test failed!")

    sys.exit(0 if success else 1)
