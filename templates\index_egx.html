<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EGX Stock Price Predictor - AI-Powered Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .prediction-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-left: 4px solid #28a745;
        }
        .metric-card {
            background: linear-gradient(45deg, #fff3cd, #ffeaa7);
            border-left: 4px solid #ffc107;
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background: #e6f3ff;
        }
        .data-source-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .data-source-card:hover {
            background: #f8f9fa;
        }
        .data-source-card.active {
            background: #e3f2fd;
            border-color: #2196f3;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark gradient-bg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>EGX Stock Predictor
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('about') }}">About</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="gradient-bg py-5">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-3">
                <i class="fas fa-brain me-3"></i>AI-Powered EGX Stock Analysis
            </h1>
            <p class="lead mb-4">Advanced LSTM Neural Networks for Egyptian Exchange Stock Prediction</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <i class="fas fa-upload fa-2x mb-2"></i>
                            <h5>CSV Upload</h5>
                            <p class="small">Upload your own data</p>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-globe fa-2x mb-2"></i>
                            <h5>TradingView</h5>
                            <p class="small">Live market data</p>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-chart-area fa-2x mb-2"></i>
                            <h5>AI Prediction</h5>
                            <p class="small">LSTM neural networks</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Prediction Form -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-lg card-hover">
                    <div class="card-header gradient-bg text-white">
                        <h4 class="mb-0"><i class="fas fa-cogs me-2"></i>Stock Prediction Configuration</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="predictionForm">
                            <!-- Data Source Selection -->
                            <div class="mb-4">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-database me-2"></i>Data Source
                                </label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card data-source-card" onclick="selectDataSource('csv')">
                                            <div class="card-body text-center">
                                                <i class="fas fa-file-csv fa-2x text-success mb-2"></i>
                                                <h6>CSV Upload</h6>
                                                <small class="text-muted">Upload your own data file</small>
                                                <input type="radio" name="data_source" value="csv" class="d-none" checked>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card data-source-card" onclick="selectDataSource('tradingview')">
                                            <div class="card-body text-center">
                                                <i class="fas fa-chart-line fa-2x text-primary mb-2"></i>
                                                <h6>TradingView</h6>
                                                <small class="text-muted">Live market data</small>
                                                <input type="radio" name="data_source" value="tradingview" class="d-none">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card data-source-card" onclick="selectDataSource('yfinance')">
                                            <div class="card-body text-center">
                                                <i class="fas fa-yahoo fa-2x text-warning mb-2"></i>
                                                <h6>Yahoo Finance</h6>
                                                <small class="text-muted">Fallback data source</small>
                                                <input type="radio" name="data_source" value="yfinance" class="d-none">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- CSV Upload Section -->
                            <div id="csvSection" class="mb-4">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-upload me-2"></i>Upload CSV File
                                </label>
                                <div class="upload-area">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h5>Drop your CSV file here or click to browse</h5>
                                    <p class="text-muted mb-3">Supported format: CSV with Date, Open, High, Low, Close, Volume columns</p>
                                    <input type="file" name="csv_file" id="csvFile" class="form-control" accept=".csv" style="display: none;">
                                    <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('csvFile').click()">
                                        <i class="fas fa-folder-open me-2"></i>Choose File
                                    </button>
                                    <div id="fileName" class="mt-2 text-success fw-bold"></div>
                                </div>
                            </div>

                            <!-- Symbol Input Section -->
                            <div id="symbolSection" class="mb-4" style="display: none;">
                                <label for="symbol" class="form-label fw-bold">
                                    <i class="fas fa-tag me-2"></i>Stock Symbol
                                </label>
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" id="symbol" name="symbol" placeholder="Enter EGX stock symbol (e.g., CIB, ETEL)">
                                    </div>
                                    <div class="col-md-4">
                                        <select class="form-select" id="popularStocks" onchange="selectPopularStock()">
                                            <option value="">Popular EGX Stocks</option>
                                            {% for symbol, name in egx_stocks.items() %}
                                                <option value="{{ symbol }}">{{ symbol }} - {{ name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Model Parameters -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="lookback" class="form-label fw-bold">
                                        <i class="fas fa-history me-2"></i>Lookback Days
                                    </label>
                                    <input type="range" class="form-range" id="lookback" name="lookback" min="10" max="60" value="30" oninput="updateValue('lookback')">
                                    <div class="d-flex justify-content-between">
                                        <small>10</small>
                                        <span id="lookbackValue" class="fw-bold text-primary">30</span>
                                        <small>60</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="epochs" class="form-label fw-bold">
                                        <i class="fas fa-sync me-2"></i>Training Epochs
                                    </label>
                                    <input type="range" class="form-range" id="epochs" name="epochs" min="5" max="20" value="10" oninput="updateValue('epochs')">
                                    <div class="d-flex justify-content-between">
                                        <small>5</small>
                                        <span id="epochsValue" class="fw-bold text-primary">10</span>
                                        <small>20</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-brain me-2"></i>Analyze & Predict
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        {% if result %}
        <div class="row mt-5">
            <div class="col-12">
                <div class="card shadow-lg">
                    <div class="card-header gradient-bg text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>Prediction Results for {{ result.symbol }}
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Model Performance Metrics -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card metric-card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-bullseye fa-2x text-success mb-2"></i>
                                        <h5 class="card-title">Accuracy</h5>
                                        <h3 class="text-success">{{ result.accuracy }}%</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card metric-card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-calculator fa-2x text-info mb-2"></i>
                                        <h5 class="card-title">RMSE</h5>
                                        <h3 class="text-info">{{ result.rmse }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card metric-card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-bar fa-2x text-warning mb-2"></i>
                                        <h5 class="card-title">MAE</h5>
                                        <h3 class="text-warning">{{ result.mae }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card metric-card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-database fa-2x text-primary mb-2"></i>
                                        <h5 class="card-title">Data Source</h5>
                                        <h3 class="text-primary">{{ result.data_source }}</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Future Predictions -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card prediction-card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-crystal-ball me-2"></i>Future Predictions (7 Days)
                                        </h5>
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Date</th>
                                                        <th>Predicted Price (EGP)</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for prediction in result.predictions %}
                                                    <tr>
                                                        <td>{{ prediction.date }}</td>
                                                        <td class="fw-bold text-success">{{ prediction.price }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-info-circle me-2"></i>Analysis Details
                                        </h5>
                                        <ul class="list-unstyled">
                                            <li><strong>Data Range:</strong> {{ result.data_range }}</li>
                                            <li><strong>Data Points:</strong> {{ result.training_params.data_points }}</li>
                                            <li><strong>Lookback Days:</strong> {{ result.training_params.lookback_days }}</li>
                                            <li><strong>Training Epochs:</strong> {{ result.training_params.epochs }}</li>
                                            <li><strong>Model Type:</strong> LSTM Neural Network</li>
                                            <li><strong>Currency:</strong> Egyptian Pound (EGP)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Charts -->
                        <div class="row">
                            {% if result.plots.predictions %}
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Prediction vs Actual</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="data:image/png;base64,{{ result.plots.predictions }}" class="img-fluid" alt="Prediction Chart">
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% if result.plots.future %}
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-crystal-ball me-2"></i>Future Predictions</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="data:image/png;base64,{{ result.plots.future }}" class="img-fluid" alt="Future Predictions Chart">
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Footer -->
    <footer class="gradient-bg text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">
                <i class="fas fa-brain me-2"></i>EGX Stock Predictor - Powered by AI & Machine Learning
            </p>
            <small>Built with TensorFlow, LSTM Neural Networks, and Flask</small>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Data source selection
        function selectDataSource(source) {
            // Remove active class from all cards
            document.querySelectorAll('.data-source-card').forEach(card => {
                card.classList.remove('active');
            });

            // Add active class to selected card
            event.currentTarget.classList.add('active');

            // Check the radio button
            document.querySelector(`input[value="${source}"]`).checked = true;

            // Show/hide sections based on selection
            if (source === 'csv') {
                document.getElementById('csvSection').style.display = 'block';
                document.getElementById('symbolSection').style.display = 'none';
            } else {
                document.getElementById('csvSection').style.display = 'none';
                document.getElementById('symbolSection').style.display = 'block';
            }
        }

        // Update range input values
        function updateValue(inputId) {
            const input = document.getElementById(inputId);
            const valueSpan = document.getElementById(inputId + 'Value');
            valueSpan.textContent = input.value;
        }

        // Popular stock selection
        function selectPopularStock() {
            const select = document.getElementById('popularStocks');
            const symbolInput = document.getElementById('symbol');
            if (select.value) {
                symbolInput.value = select.value;
            }
        }

        // File upload handling
        document.getElementById('csvFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                document.getElementById('fileName').textContent = `Selected: ${fileName}`;
            }
        });

        // Initialize default data source
        document.addEventListener('DOMContentLoaded', function() {
            selectDataSource('csv');
        });
    </script>
</body>
</html>
