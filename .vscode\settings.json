{"python.defaultInterpreterPath": "./venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.analysis.typeCheckingMode": "off", "python.analysis.autoImportCompletions": true, "python.analysis.extraPaths": ["./venv/Lib/site-packages"], "pylance.insidersChannel": "off", "python.analysis.diagnosticMode": "workspace", "python.analysis.stubPath": "./venv/Lib/site-packages", "python.analysis.diagnosticSeverityOverrides": {"reportMissingImports": "none", "reportMissingModuleSource": "none", "reportOptionalSubscript": "none", "reportOptionalMemberAccess": "none", "reportAttributeAccessIssue": "none", "reportArgumentType": "none"}}