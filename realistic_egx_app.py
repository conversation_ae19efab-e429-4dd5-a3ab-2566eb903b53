"""
Realistic EGX Stock Analysis Web Application
Provides honest, realistic stock analysis instead of fake predictions
"""

from flask import Flask, render_template, request, jsonify
import json
from realistic_stock_predictor import RealisticEGXPredictor
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import base64
import io
import pandas as pd
import numpy as np

app = Flask(__name__)

# HTML Template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏛️ Realistic EGX Stock Analysis</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .subtitle {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .warning-banner {
            background: #e74c3c;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        .content {
            padding: 30px;
        }
        .input-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        input, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .results {
            margin-top: 30px;
        }
        .metric-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .metric-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        .metric-value {
            font-size: 1.3em;
            font-weight: bold;
        }
        .bullish { color: #27ae60; }
        .bearish { color: #e74c3c; }
        .neutral { color: #f39c12; }
        .disclaimer {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .disclaimer h3 {
            color: #856404;
            margin-top: 0;
        }
        .loading {
            text-align: center;
            padding: 40px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ Realistic EGX Stock Analysis</h1>
            <p class="subtitle">Honest Technical Analysis - No Fake Predictions</p>
        </div>
        
        <div class="warning-banner">
            ⚠️ This provides technical analysis only - NOT investment advice. Stock markets are unpredictable!
        </div>
        
        <div class="content">
            <div class="input-section">
                <h2>📊 Stock Analysis</h2>
                <form id="analysisForm">
                    <div class="form-group">
                        <label for="symbol">EGX Stock Symbol:</label>
                        <input type="text" id="symbol" name="symbol" placeholder="e.g., COMI, CIB, ETEL" required>
                    </div>
                    <button type="submit" class="btn">🔍 Analyze Stock</button>
                </form>
            </div>
            
            <div id="results" class="results" style="display: none;">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>

    <script>
        document.getElementById('analysisForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const symbol = document.getElementById('symbol').value.toUpperCase();
            const resultsDiv = document.getElementById('results');
            
            // Show loading
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Analyzing ${symbol}... This may take a moment.</p>
                </div>
            `;
            
            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ symbol: symbol })
                });
                
                const data = await response.json();
                displayResults(data);
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="metric-card">
                        <h3 style="color: #e74c3c;">❌ Error</h3>
                        <p>Failed to analyze stock. Please try again.</p>
                    </div>
                `;
            }
        });
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            if (data.status === 'error') {
                resultsDiv.innerHTML = `
                    <div class="metric-card">
                        <h3 style="color: #e74c3c;">❌ ${data.message}</h3>
                        <p><strong>Recommendation:</strong> ${data.recommendation}</p>
                    </div>
                `;
                return;
            }
            
            const trend = data.technical_analysis.trend;
            const trendClass = trend === 'Bullish' ? 'bullish' : trend === 'Bearish' ? 'bearish' : 'neutral';
            
            resultsDiv.innerHTML = `
                <h2>📈 Analysis Results for ${data.symbol}</h2>
                
                <div class="metric-card">
                    <div class="metric-title">💰 Current Price</div>
                    <div class="metric-value">${data.current_price.toFixed(2)} ${data.currency}</div>
                    <small>Last updated: ${data.last_updated}</small>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">📊 Technical Analysis</div>
                    <p><strong>Trend:</strong> <span class="${trendClass}">${data.technical_analysis.trend}</span></p>
                    <p><strong>RSI Signal:</strong> ${data.technical_analysis.rsi_signal}</p>
                    <p><strong>Support Level:</strong> ${data.technical_analysis.support_level.toFixed(2)} EGP</p>
                    <p><strong>Resistance Level:</strong> ${data.technical_analysis.resistance_level.toFixed(2)} EGP</p>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">⚡ Risk Assessment</div>
                    <p><strong>Volatility:</strong> ${data.risk_assessment.volatility.toFixed(2)}%</p>
                    <p><strong>Risk Level:</strong> ${data.risk_assessment.risk_level}</p>
                </div>
                
                <div class="disclaimer">
                    <h3>⚠️ Important Disclaimer</h3>
                    <p><strong>Confidence Level:</strong> ${data.realistic_outlook.confidence}</p>
                    <p><strong>Note:</strong> ${data.realistic_outlook.disclaimer}</p>
                    <ul>
                        <li>This is technical analysis based on historical price data only</li>
                        <li>Stock prices are influenced by many unpredictable factors</li>
                        <li>Past performance does not guarantee future results</li>
                        <li>Always consult with a financial advisor before making investment decisions</li>
                    </ul>
                </div>
            `;
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return HTML_TEMPLATE

@app.route('/analyze', methods=['POST'])
def analyze_stock():
    try:
        data = request.get_json()
        symbol = data.get('symbol', '').upper()
        
        if not symbol:
            return jsonify({
                'status': 'error',
                'message': 'Please provide a stock symbol',
                'recommendation': 'Enter a valid EGX stock symbol'
            })
        
        # Create predictor and analyze
        predictor = RealisticEGXPredictor(symbol)
        result = predictor.realistic_prediction_analysis()
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Analysis failed: {str(e)}',
            'recommendation': 'Please try again or contact support'
        })

if __name__ == '__main__':
    print("🏛️ Starting Realistic EGX Stock Analysis App...")
    print("📊 This app provides honest technical analysis")
    print("⚠️  No fake predictions - only real market data analysis")
    print("🌐 Access at: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=False)
