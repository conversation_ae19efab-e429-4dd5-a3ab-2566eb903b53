import requests
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime
import time
import random
import logging
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from dateutil import tz
from selenium.common.exceptions import (
    TimeoutException,
    WebDriverException,
    NoSuchElementException,
    StaleElementReferenceException
)

# Import error handling utilities
try:
    from app.utils.error_handling import (
        ScrapingError,
        ResourceError,
        ConfigError,
        APIError,
        retry_on_exception,
        handle_exception,
        log_exception,
        robust_function,
        ErrorHandler
    )
    ERROR_HANDLING_AVAILABLE = True
except ImportError:
    ERROR_HANDLING_AVAILABLE = False
    logging.warning("Error handling utilities not available. Using basic error handling.")

    # Define fallback error classes if not available
    class ScrapingError(Exception):
        pass

    class ResourceError(Exception):
        pass

    class ConfigError(Exception):
        pass

    class APIError(Exception):
        pass

# Import retry utilities
try:
    from app.utils.retry import retry_with_backoff
    RETRY_AVAILABLE = True
except ImportError:
    RETRY_AVAILABLE = False
    logging.warning("Retry utilities not available. Scraping operations may be less reliable.")

# Import Selenium components
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    logging.warning("Selenium not available. Falling back to requests.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PriceScraper:
    """
    Scraper for getting live stock prices from various sources
    """

    def __init__(self, source="tradingview", use_real_time=False, username=None, password=None, tradingview_email=None, tradingview_password=None, is_gmail=False):
        """
        Initialize PriceScraper with source and options

        Args:
            source (str): Source for price data (tradingview, mubasher)
            use_real_time (bool): Whether to use real-time data from TradingView (requires login)
            username (str): Legacy parameter, use tradingview_email instead
            password (str): Legacy parameter, use tradingview_password instead
            tradingview_email (str): TradingView email/username
            tradingview_password (str): TradingView password
            is_gmail (bool): Whether the TradingView account uses Gmail login
        """
        self.source = source
        self.use_real_time = use_real_time

        # Support both new and legacy parameter names for backwards compatibility
        self.tradingview_email = tradingview_email or username
        self.tradingview_password = tradingview_password or password
        self.is_logged_in = False

        # Gmail detection - if the email ends with @gmail.com or is_gmail is True
        if self.tradingview_email:
            self.is_gmail = self.tradingview_email.lower().endswith('@gmail.com') or is_gmail
            logger.info(f"Account type: {'Google' if self.is_gmail else 'Standard TradingView'}")
        else:
            self.is_gmail = is_gmail

        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
        }

        self.driver = None

        # Initialize Selenium for TradingView real-time data (if available)
        if SELENIUM_AVAILABLE and source.lower() == 'tradingview':
            try:
                self.initialize_driver()
                # If real-time data is requested and credentials are provided, log in
                if self.use_real_time and self.tradingview_email and self.tradingview_password:
                    if self.is_gmail:
                        logger.info("Using Google authentication flow for TradingView")
                        self.login_to_tradingview_via_google()
                    else:
                        logger.info("Using standard authentication flow for TradingView")
                        self.login_to_tradingview()
            except Exception as e:
                logger.error(f"Error initializing Selenium driver: {str(e)}")

    def __del__(self):
        """
        Destructor to ensure WebDriver is closed when the object is garbage collected
        """
        self.close_driver()

    def close_driver(self):
        """
        Explicitly close the WebDriver to free up resources

        This method should be called when the scraper is no longer needed
        to ensure proper cleanup of resources.
        """
        if self.driver is not None:
            if ERROR_HANDLING_AVAILABLE:
                with ErrorHandler(log_level=logging.WARNING) as handler:
                    logger.info("Closing WebDriver")
                    self.driver.quit()
                    self.driver = None
                    return True

                # If we get here, there was an exception
                if handler.exception:
                    logger.error(f"Error closing WebDriver: {str(handler.exception)}")
                    # Still set driver to None to prevent further usage
                    self.driver = None
                    return False
            else:
                # Fallback to basic error handling
                try:
                    logger.info("Closing WebDriver")
                    self.driver.quit()
                except Exception as e:
                    logger.error(f"Error closing WebDriver: {str(e)}")
                finally:
                    self.driver = None

            return True

    # Add property getters/setters for backward compatibility
    @property
    def username(self):
        """Backward compatibility for username attribute"""
        return self.tradingview_email

    @username.setter
    def username(self, value):
        """Backward compatibility for username attribute"""
        self.tradingview_email = value

    @property
    def password(self):
        """Backward compatibility for password attribute"""
        return self.tradingview_password

    @password.setter
    def password(self, value):
        """Backward compatibility for password attribute"""
        self.tradingview_password = value

    def initialize_driver(self):
        """
        Initialize Selenium Chrome driver
        """
        if not SELENIUM_AVAILABLE:
            logger.warning("Selenium not available. Cannot initialize driver.")
            return

        # Use enhanced error handling if available
        if ERROR_HANDLING_AVAILABLE:
            with ErrorHandler(log_level=logging.ERROR, raise_exception=True) as handler:
                # Configure Chrome options
                chrome_options = Options()

                # Performance and stability options
                chrome_options.add_argument("--disable-gpu")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")

                # Privacy and security options - IMPORTANT: These fix the "not secure" warnings
                chrome_options.add_argument("--ignore-certificate-errors")
                chrome_options.add_argument("--ignore-ssl-errors")
                chrome_options.add_argument("--allow-running-insecure-content")  # Allow mixed content
                chrome_options.add_argument("--disable-web-security")  # Disable CORS and other security features that might block content

                # Window size - IMPORTANT: set a reasonable window size for TradingView
                chrome_options.add_argument("--window-size=1920,1080")

                # User agent - use a modern, common user agent
                chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

                # Anti-automation detection (only use ONE method to avoid conflicts)
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # IMPORTANT: NEVER run headless when using real-time data as login requires visible browser
                # During debugging, keep browser visible to see what's happening
                # chrome_options.add_argument("--headless=new")  # Commented out to keep browser visible

                # Add SSL configuration
                chrome_options.add_argument("--ssl-version-max=tls1.3")
                chrome_options.add_argument("--ssl-version-min=tls1")

                # Try multiple locations for chromedriver
                driver_locations = [
                    'chromedriver.exe',  # In current directory
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), 'chromedriver.exe'),  # In scrapers folder
                    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'chromedriver.exe'),  # In project root
                ]

                chrome_driver_path = None
                for location in driver_locations:
                    if os.path.exists(location):
                        chrome_driver_path = location
                        logger.info(f"Found ChromeDriver at {chrome_driver_path}")
                        break

                if not chrome_driver_path:
                    raise ResourceError("ChromeDriver not found in any expected location",
                                       details={"searched_locations": driver_locations})

                # Set up accept insecure certs in options
                # In Selenium 4, only acceptInsecureCerts is supported, not acceptSslCerts
                chrome_options.set_capability("acceptInsecureCerts", True)

                # Initialize driver - For Selenium 4
                service = Service(chrome_driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)

                # Set page load timeout
                self.driver.set_page_load_timeout(30)

                logger.info("Selenium driver initialized successfully")

            # If we get here and there was an exception, handle it
            if handler.exception:
                logger.error(f"Error initializing Chrome driver: {str(handler.exception)}")
                self.driver = None
                # Re-raise as ResourceError for better handling upstream
                if not isinstance(handler.exception, ResourceError):
                    raise ResourceError(f"Failed to initialize WebDriver: {str(handler.exception)}",
                                      details={"original_error": str(handler.exception)})
                raise
        else:
            # Fallback to basic error handling
            try:
                # Configure Chrome options
                chrome_options = Options()

                # Performance and stability options
                chrome_options.add_argument("--disable-gpu")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")

                # Privacy and security options - IMPORTANT: These fix the "not secure" warnings
                chrome_options.add_argument("--ignore-certificate-errors")
                chrome_options.add_argument("--ignore-ssl-errors")
                chrome_options.add_argument("--allow-running-insecure-content")  # Allow mixed content
                chrome_options.add_argument("--disable-web-security")  # Disable CORS and other security features that might block content

                # Window size - IMPORTANT: set a reasonable window size for TradingView
                chrome_options.add_argument("--window-size=1920,1080")

                # User agent - use a modern, common user agent
                chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

                # Anti-automation detection (only use ONE method to avoid conflicts)
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # IMPORTANT: NEVER run headless when using real-time data as login requires visible browser
                # During debugging, keep browser visible to see what's happening
                # chrome_options.add_argument("--headless=new")  # Commented out to keep browser visible

                # Add SSL configuration
                chrome_options.add_argument("--ssl-version-max=tls1.3")
                chrome_options.add_argument("--ssl-version-min=tls1")

                # Try multiple locations for chromedriver
                driver_locations = [
                    'chromedriver.exe',  # In current directory
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), 'chromedriver.exe'),  # In scrapers folder
                    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'chromedriver.exe'),  # In project root
                ]

                chrome_driver_path = None
                for location in driver_locations:
                    if os.path.exists(location):
                        chrome_driver_path = location
                        logger.info(f"Found ChromeDriver at {chrome_driver_path}")
                        break

                if not chrome_driver_path:
                    logger.error("ChromeDriver not found in any expected location")
                    return

                # Set up accept insecure certs in options
                # In Selenium 4, only acceptInsecureCerts is supported, not acceptSslCerts
                chrome_options.set_capability("acceptInsecureCerts", True)

                # Initialize driver - For Selenium 4
                service = Service(chrome_driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)

                # Set page load timeout
                self.driver.set_page_load_timeout(30)

                logger.info("Selenium driver initialized successfully")

            except Exception as e:
                logger.error(f"Error initializing Chrome driver: {str(e)}")
                self.driver = None

    def login_to_tradingview(self):
        """
        Login to TradingView using standard username/password

        This improved implementation provides better handling of the TradingView login process
        with clearer user instructions and more robust error handling.
        """
        if not SELENIUM_AVAILABLE or self.driver is None:
            logger.warning("Selenium not available or driver not initialized. Cannot login.")
            return False

        if not self.tradingview_email or not self.tradingview_password:
            logger.warning("TradingView credentials not provided")
            return False

        try:
            # Set longer timeouts for auth flow
            self.driver.set_page_load_timeout(60)
            self.driver.set_script_timeout(30)

            # Check if already logged in
            if self._check_if_logged_in():
                logger.info("Already logged in to TradingView")
                self.is_logged_in = True
                return True

            # Go directly to the sign-in page for more reliable login
            logger.info("Navigating directly to TradingView sign-in page")
            self.driver.get("https://www.tradingview.com/accounts/signin/")
            time.sleep(3)

            # Take a screenshot for debugging
            try:
                self.driver.save_screenshot("login_page.png")
                logger.info("Saved screenshot of login page")
            except Exception as e:
                logger.warning(f"Could not save screenshot: {str(e)}")

            # Display a helper message for the user
            self.driver.execute_script("""
                let div = document.createElement('div');
                div.style.position = 'fixed';
                div.style.top = '0';
                div.style.left = '0';
                div.style.width = '100%';
                div.style.padding = '10px';
                div.style.background = '#ffeb3b';
                div.style.color = 'black';
                div.style.zIndex = '9999';
                div.style.fontSize = '16px';
                div.style.textAlign = 'center';
                div.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                div.innerHTML = '<b>AI Stocks Bot - TradingView Login</b><br>Please wait while we log you in automatically, or complete the login form if prompted.';
                document.body.appendChild(div);
            """)

            # Now we should be on the sign-in form
            try:
                # First try to find the username field (TradingView uses username, not email for standard accounts)
                username_field = None
                username_selectors = [
                    # Standard TradingView login selectors
                    (By.XPATH, "//input[@name='username']"),
                    (By.XPATH, "//input[contains(@placeholder, 'Username')]"),
                    (By.XPATH, "//input[contains(@placeholder, 'Email or Username')]"),
                    (By.XPATH, "//input[contains(@placeholder, 'Email')]"),
                    (By.XPATH, "//input[@type='text']"),

                    # Generic selectors
                    (By.CSS_SELECTOR, "input.tv-control-material-input"),
                    (By.CSS_SELECTOR, "form input[type='text']"),
                    (By.CSS_SELECTOR, "input[name='username']"),
                    (By.CSS_SELECTOR, ".tv-signin-dialog__input"),
                    (By.CSS_SELECTOR, ".js-signin-username")
                ]

                for selector_type, selector in username_selectors:
                    try:
                        username_field = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((selector_type, selector))
                        )
                        if username_field:
                            logger.info(f"Found username field using selector: {selector}")
                            break
                    except:
                        continue

                if not username_field:
                    logger.warning("Could not find username field automatically")

                    # Take a screenshot for debugging
                    try:
                        self.driver.save_screenshot("login_form_not_found.png")
                        logger.info("Saved screenshot of login form to login_form_not_found.png")
                    except Exception as e:
                        logger.warning(f"Could not save screenshot: {str(e)}")

                    # Try to find any input field as a fallback
                    try:
                        username_field = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.TAG_NAME, "input"))
                        )
                        logger.info("Found generic input field as fallback")
                    except:
                        logger.error("Could not find any input field")

                if username_field:
                    # Clear and enter username with human-like typing
                    username_field.clear()
                    logger.info(f"Entering username: {self.tradingview_email}")
                    for char in self.tradingview_email:
                        username_field.send_keys(char)
                        time.sleep(random.uniform(0.05, 0.15))

                    # Press Tab to move to password field (more reliable than trying to find it)
                    username_field.send_keys(Keys.TAB)
                    time.sleep(1)

                    # Now the active element should be the password field
                    active_element = self.driver.switch_to.active_element

                    # Enter password with human-like typing
                    active_element.clear()
                    logger.info("Entering password")
                    for char in self.tradingview_password:
                        active_element.send_keys(char)
                        time.sleep(random.uniform(0.05, 0.15))

                    # Press Enter to submit the form (more reliable than finding the button)
                    logger.info("Submitting login form")
                    active_element.send_keys(Keys.RETURN)

                    # Wait for login to process
                    time.sleep(5)

                    # Check for any verification requirements
                    page_source = self.driver.page_source.lower()
                    if any(keyword in page_source for keyword in ["verify", "security", "unusual", "confirm", "captcha"]):
                        logger.warning("TradingView verification required. User interaction needed.")

                        # Show a more prominent message in the browser to help the user
                        self.driver.execute_script("""
                            // Remove any existing messages
                            let existingMsg = document.querySelector('#ai-stocks-bot-msg');
                            if (existingMsg) existingMsg.remove();

                            // Create new message
                            let div = document.createElement('div');
                            div.id = 'ai-stocks-bot-msg';
                            div.style.position = 'fixed';
                            div.style.top = '0';
                            div.style.left = '0';
                            div.style.width = '100%';
                            div.style.padding = '15px';
                            div.style.background = '#f44336';
                            div.style.color = 'white';
                            div.style.zIndex = '9999';
                            div.style.fontSize = '18px';
                            div.style.textAlign = 'center';
                            div.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                            div.innerHTML = '<b>⚠️ VERIFICATION REQUIRED ⚠️</b><br>Please complete the verification steps in this window<br>The app will continue automatically once verified';
                            document.body.appendChild(div);
                        """)

                        # Wait for the user to complete verification (up to 2 minutes)
                        for i in range(24):  # 24 * 5 seconds = 2 minutes
                            time.sleep(5)
                            if self._check_if_logged_in():
                                logger.info("Successfully logged in to TradingView after verification")
                                self.is_logged_in = True
                                return True
                            logger.info(f"Waiting for verification completion... ({i+1}/24)")
                else:
                    logger.error("Could not find email field")
            except Exception as e:
                logger.error(f"Error during TradingView login form interaction: {str(e)}")

            # Final check if login was successful
            if self._check_if_logged_in():
                logger.info("Successfully logged in to TradingView")
                self.is_logged_in = True
                return True

            # Login failed, provide clear instructions for manual login
            logger.warning("Automated login failed. Prompting user for manual login.")

            # Navigate to a clean login page
            self.driver.get("https://www.tradingview.com/accounts/signin/")
            time.sleep(2)

            # Take a screenshot for debugging
            try:
                self.driver.save_screenshot("login_failed.png")
                logger.info("Saved screenshot of failed login to login_failed.png")
            except Exception as e:
                logger.warning(f"Could not save screenshot: {str(e)}")

            # Add clear instructions for manual login
            self.driver.execute_script("""
                // Remove any existing messages
                let existingMsg = document.querySelector('#ai-stocks-bot-msg');
                if (existingMsg) existingMsg.remove();

                // Create new message with detailed instructions
                let div = document.createElement('div');
                div.id = 'ai-stocks-bot-msg';
                div.style.position = 'fixed';
                div.style.top = '0';
                div.style.left = '0';
                div.style.width = '100%';
                div.style.padding = '15px';
                div.style.background = '#ff9800';
                div.style.color = 'black';
                div.style.zIndex = '9999';
                div.style.fontSize = '16px';
                div.style.textAlign = 'center';
                div.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                div.innerHTML = `
                    <b>⚠️ MANUAL LOGIN REQUIRED ⚠️</b><br>
                    <p>Please log in to TradingView with your standard account:</p>
                    <ol style="text-align: left; display: inline-block;">
                        <li>Enter your username: <b>${document.createTextNode(arguments[0]).textContent}</b></li>
                        <li>Enter your password</li>
                        <li>Click the Sign In button</li>
                        <li>Complete any verification if prompted</li>
                    </ol>
                    <p>Note: TradingView standard accounts use username (not email) for login</p>
                    <p>The app will continue automatically once you're logged in</p>
                `;
                document.body.appendChild(div);
            """, self.tradingview_email)

            # Give user time to log in manually (up to 2 minutes)
            for i in range(24):  # 24 * 5 seconds = 2 minutes
                time.sleep(5)
                if self._check_if_logged_in():
                    logger.info("Successfully logged in to TradingView manually")
                    self.is_logged_in = True
                    return True
                logger.info(f"Waiting for manual login... ({i+1}/24)")

            logger.error("Failed to log in to TradingView even after manual login attempt")
            return False

        except Exception as e:
            logger.error(f"Error during TradingView login: {str(e)}")
            return False

    def login_to_tradingview_via_google(self):
        """
        Login to TradingView using Google authentication

        This method attempts to log in to TradingView using the Google OAuth flow.
        Google often implements strict security measures that can block automated login.
        """
        if not SELENIUM_AVAILABLE or self.driver is None:
            logger.warning("Selenium not available or driver not initialized. Cannot login.")
            return False

        if not self.tradingview_email or not self.tradingview_password:
            logger.warning("Google credentials not provided. Cannot login to TradingView.")
            return False

        try:
            # Set longer timeouts for authentication flow
            self.driver.set_page_load_timeout(60)
            self.driver.set_script_timeout(30)

            # Method 1: Try direct navigation to TradingView's home page first
            self.driver.get("https://www.tradingview.com/")
            time.sleep(2)

            # Check if we're already logged in
            if self._check_if_logged_in():
                logger.info("Already logged in to TradingView")
                return True

            # Look for and click the "Sign in" button
            try:
                sign_in_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Sign in') or contains(@class, 'tv-header__user-menu-button')]"))
                )
                sign_in_button.click()
                time.sleep(2)

                # Now look for the Google sign-in option
                google_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), 'Google')]/parent::button"))
                )
                google_button.click()
                time.sleep(3)
            except Exception as e:
                logger.warning(f"Could not find sign in buttons on homepage: {str(e)}")
                # Fall back to direct OAuth URL
                self.driver.get("https://www.tradingview.com/accounts/signin/")
                time.sleep(2)

                try:
                    google_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), 'Google')]/parent::button"))
                    )
                    google_button.click()
                    time.sleep(3)
                except Exception as e2:
                    logger.warning(f"Could not find Google button on signin page: {str(e2)}")
                    # Last resort - go directly to Google OAuth
                    self.driver.get("https://www.tradingview.com/accounts/google/login/")
                    time.sleep(3)

            # Handle Google login flow
            current_url = self.driver.current_url

            # Check if we're now on a Google login page
            if "google.com" in current_url or "accounts.google.com" in current_url:
                try:
                    # First try to find the email input field
                    google_email = WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.ID, "identifierId"))
                    )

                    # Type the email using human-like typing
                    for char in self.tradingview_email:
                        google_email.send_keys(char)
                        time.sleep(random.uniform(0.05, 0.15))  # Randomize typing speed

                    time.sleep(1)

                    # Find and click the "Next" button
                    next_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'Next') or contains(@id, 'identifierNext')]"))
                    )
                    next_button.click()

                    # Wait for password field to appear
                    time.sleep(3)

                    # Find the password field
                    google_password = WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.XPATH, "//input[@type='password']"))
                    )

                    # Type the password using human-like typing
                    for char in self.tradingview_password:
                        google_password.send_keys(char)
                        time.sleep(random.uniform(0.05, 0.15))  # Randomize typing speed

                    time.sleep(1)

                    # Find and click the password "Next" button
                    password_next = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'Next') or contains(@id, 'passwordNext')]"))
                    )
                    password_next.click()

                    # Wait for potential 2FA or verification
                    time.sleep(5)

                    # If we see words like "unusual activity", "verify it's you", etc., we need manual help
                    page_source = self.driver.page_source.lower()
                    if any(keyword in page_source for keyword in ["unusual", "verify", "security", "protect", "recovery", "backup"]):
                        logger.warning("Google security verification detected. User interaction required in the browser window.")

                        # Show a message in the browser to help the user
                        self.driver.execute_script("""
                            let div = document.createElement('div');
                            div.style.position = 'fixed';
                            div.style.top = '0';
                            div.style.left = '0';
                            div.style.width = '100%';
                            div.style.padding = '10px';
                            div.style.background = 'yellow';
                            div.style.color = 'black';
                            div.style.zIndex = '9999';
                            div.style.fontSize = '16px';
                            div.style.textAlign = 'center';
                            div.innerHTML = '<b>Please complete the Google verification in this window</b><br>The app will continue automatically once verified';
                            document.body.appendChild(div);
                        """)

                        # Give user time to complete verification manually (up to 2 minutes)
                        for _ in range(24):  # 24 * 5 seconds = 2 minutes
                            time.sleep(5)
                            # Check if we're now on TradingView
                            if "tradingview.com" in self.driver.current_url:
                                logger.info("Successfully navigated to TradingView after manual verification")
                                break

                except Exception as e:
                    logger.error(f"Error during Google login: {str(e)}")

            # Wait for redirect back to TradingView
            timeout = time.time() + 60  # 1 minute timeout
            while time.time() < timeout:
                if "tradingview.com" in self.driver.current_url:
                    # Wait for the page to fully load
                    time.sleep(5)
                    break
                time.sleep(1)

            # Check if login was successful
            if self._check_if_logged_in():
                logger.info("Successfully logged in to TradingView via Google")
                return True

            logger.warning("Failed to log in to TradingView via Google. The browser window is still open for manual login.")
            # Add instructions for manual login
            self.driver.execute_script("""
                let div = document.createElement('div');
                div.style.position = 'fixed';
                div.style.top = '0';
                div.style.left = '0';
                div.style.width = '100%';
                div.style.padding = '10px';
                div.style.background = 'yellow';
                div.style.color = 'black';
                div.style.zIndex = '9999';
                div.style.fontSize = '16px';
                div.style.textAlign = 'center';
                div.innerHTML = '<b>Please log in to TradingView manually in this window</b><br>The app will continue automatically once logged in';
                document.body.appendChild(div);
            """)

            # Give user time to log in manually (up to 2 minutes)
            for _ in range(24):  # 24 * 5 seconds = 2 minutes
                time.sleep(5)
                if self._check_if_logged_in():
                    logger.info("Successfully logged in to TradingView manually")
                    return True

            logger.error("Failed to log in to TradingView even after manual login attempt")
            return False

        except Exception as e:
            logger.error(f"Error during TradingView Google login: {str(e)}")
            return False

    def _check_if_logged_in(self):
        """
        Check if we're logged in to TradingView by looking for user-specific elements.

        This improved implementation uses multiple methods to verify login status
        and provides more detailed logging.

        Returns:
            bool: True if logged in, False otherwise
        """
        try:
            # First check the URL - if we're on a signin page, we're definitely not logged in
            current_url = self.driver.current_url.lower()
            if "signin" in current_url or "sign-in" in current_url or "login" in current_url:
                logger.info("Currently on a login page, definitely not logged in")
                return False

            # Method 1: Check for user-specific UI elements
            try:
                # Multiple selectors to check for login status (updated for latest TradingView UI)
                login_indicators = [
                    "button[aria-label='User']",
                    ".tv-header__user-menu-button",
                    ".header-user-menu-button",
                    ".js-user-menu-button",
                    "img.tv-header__icon.tv-header__icon--user",
                    ".avatar-tOUjY9ez",  # Newer TradingView class for user avatar
                    "[data-name='user-menu-button']",
                    ".userButton-ZzR2Jeft",  # Very new TradingView class
                    ".userMenu-eG2Hnhqh"  # Another new TradingView class
                ]

                for selector in login_indicators:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        logger.info(f"Found login indicator using selector: {selector}")
                        return True

                # Also check for premium badge which is only visible for paid accounts
                premium_indicators = [
                    ".tv-header__dropdown-text.tv-header__dropdown-text--premium",
                    "span.tv-header-user-menu-button__pro-badge",
                    "i.tv-header__dropdown--pro",
                    ".header-user-menu__pro-badge",
                    "[data-role='user-pro-badge']",
                    ".pro-3NG-zyQe"  # New TradingView class for pro badge
                ]

                for selector in premium_indicators:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        logger.info(f"Found premium indicator using selector: {selector}")
                        return True
            except Exception as e:
                logger.warning(f"Error checking login UI elements: {str(e)}")

            # Method 2: Check for elements that only appear when logged out
            try:
                logout_indicators = [
                    "a[href='/accounts/signin/']",
                    "a[href*='signin']",
                    "button:contains('Sign in')",
                    "button:contains('Log in')",
                    ".tv-header__link--signin",
                    ".tv-header__link--signup"
                ]

                for selector in logout_indicators:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and elements[0].is_displayed():
                        logger.info(f"Found logout indicator using selector: {selector}")
                        return False
            except Exception as e:
                logger.warning(f"Error checking logout UI elements: {str(e)}")

            # Method 3: Check JavaScript variables
            try:
                # Check if user data exists in JavaScript variables
                is_logged_in = self.driver.execute_script("""
                    // Try to find user info in different ways
                    if (window.user && window.user.username) return true;
                    if (window.TradingView && window.TradingView.User && window.TradingView.User.username) return true;

                    // Check for user-specific elements
                    if (document.querySelector('[data-name="user-menu-button"]')) return true;
                    if (document.querySelector('.userButton-ZzR2Jeft')) return true;

                    // Check if there are premium/pro elements visible
                    const proElems = document.querySelectorAll('[data-role="user-pro-badge"], [class*="badge"], [class*="pro"]');
                    if (proElems.length > 0) return true;

                    // Check for sign-in/sign-up buttons (indicates not logged in)
                    const signInButtons = document.querySelectorAll('a[href*="signin"], button:contains("Sign in"), .tv-header__link--signin');
                    if (signInButtons.length > 0 && window.getComputedStyle(signInButtons[0]).display !== 'none') return false;

                    // If we can't determine, return null
                    return null;
                """)

                if is_logged_in == True:
                    logger.info("Found logged in status via JavaScript")
                    return True
                elif is_logged_in == False:
                    logger.info("Found logged out status via JavaScript")
                    return False
                # If null, continue to other methods
            except Exception as e:
                logger.warning(f"Error checking login via JavaScript: {str(e)}")

            # Method 4: Try to access a protected page
            try:
                # Only do this if we're not already on a chart page
                if not "/chart/" in current_url:
                    logger.info("Checking login by navigating to chart page...")
                    original_url = self.driver.current_url
                    self.driver.get("https://www.tradingview.com/chart/")
                    time.sleep(3)

                    # If we get redirected to a signin URL, we're not logged in
                    new_url = self.driver.current_url.lower()
                    if "signin" in new_url or "sign-in" in new_url or "login" in new_url:
                        logger.info("Redirected to signin page, not logged in")
                        # Navigate back to where we were
                        self.driver.get(original_url)
                        return False

                    # Check for chart elements that would only exist for logged-in users
                    chart_elements = [
                        ".js-chart-settings",
                        ".chart-container",
                        ".layout__area--center",
                        ".chart-page",
                        ".chart-controls-bar"
                    ]

                    for selector in chart_elements:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            logger.info(f"Found chart element using selector: {selector}")
                            # Navigate back to where we were
                            self.driver.get(original_url)
                            return True

                    # Navigate back to where we were
                    self.driver.get(original_url)
            except Exception as e:
                logger.warning(f"Error checking login via protected page: {str(e)}")

            # Method 5: Check for real-time data access (only available to logged-in users)
            try:
                is_real_time = self.driver.execute_script("""
                    // Check for real-time indicators
                    if (document.querySelector('.js-data-mode-real-time, .js-real-time-badge, .paid-feature-badge')) {
                        return true;
                    }
                    return false;
                """)

                if is_real_time:
                    logger.info("Found real-time data access, user is logged in")
                    return True
            except Exception as e:
                logger.warning(f"Error checking real-time data access: {str(e)}")

            # If we've tried all methods and couldn't confirm login, assume not logged in
            logger.info("Could not confirm login status after all checks, assuming not logged in")
            return False

        except Exception as e:
            logger.error(f"Error checking login status: {str(e)}")
            return False

    def _get_tradingview_price_selenium(self, symbol):
        """
        Get price from TradingView using Selenium

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Dictionary with price data
        """
        try:
            # Format the symbol correctly for TradingView URL
            # For EGX stocks, the URL format is EGX-SYMBOL (with a hyphen, not a colon)
            if ':' in symbol:
                # Symbol already has exchange prefix
                exchange, ticker = symbol.split(':')
                formatted_symbol = f"{exchange}-{ticker}"
            else:
                # Assume EGX exchange if not specified
                formatted_symbol = f"EGX-{symbol}"

            # TradingView URL
            url = f"https://www.tradingview.com/symbols/{formatted_symbol}/"

            # Define a function for navigating to the page that can be retried
            def navigate_to_page():
                logger.info(f"Navigating to {url}")
                self.driver.get(url)

                # Wait for the page to load (up to 10 seconds)
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )

                # Take a screenshot for debugging
                try:
                    self.driver.save_screenshot(f"{symbol}_page.png")
                    logger.info(f"Saved screenshot of {symbol} page to {symbol}_page.png")
                except Exception as e:
                    logger.warning(f"Could not save screenshot: {str(e)}")

                # Additional short wait to ensure JS loads
                time.sleep(2)

                return True

            try:
                # Use retry mechanism if available
                if RETRY_AVAILABLE:
                    try:
                        retry_with_backoff(
                            max_retries=3,
                            backoff_factor=2.0,
                            initial_wait=1.0,
                            max_wait=15.0,
                            jitter=True,
                            exceptions=(TimeoutException, WebDriverException)
                        )(navigate_to_page)()
                    except Exception as e:
                        logger.error(f"Error navigating to {url} after retries: {str(e)}")
                        return self._generate_sample_price(symbol)
                else:
                    # Fallback to non-retry approach
                    navigate_to_page()
            except Exception as e:
                logger.error(f"Error navigating to {url}: {str(e)}")
                return self._generate_sample_price(symbol)

            # First try to extract price from the header (most reliable for the latest UI)
            header_price = self._extract_price_from_header(symbol)
            if header_price is not None:
                price = header_price
                logger.info(f"Successfully extracted price from header for {symbol}: {price}")
            else:
                # If header extraction fails, try multiple selectors
                logger.info("Header price extraction failed, trying CSS selectors...")

                # Try multiple selectors to find the price (updated for latest TradingView UI)
                selectors = [
                    # New TradingView selectors (2023-2024)
                    '.last-JWXMzmRr',  # Latest selector from your screenshot
                    '.last-JWXMzmRr.js-symbol-last',
                    '.last-zoF9r75I.js-symbol-last',
                    '.lastValue-dxGZjzSC',
                    '.lastValueValue-dxGZjzSC',

                    # Chart page selectors
                    '.chart-markup-table.pane-legend-line.main > span:nth-child(2)',
                    '.chart-markup-table.pane-legend-line.main .pane-legend-line__value',
                    '.chart-status-wrapper .price-qQzSCWsb',

                    # Symbol page selectors
                    '[data-name="last"]',
                    '.tv-symbol-price-quote__value',
                    '.tv-symbol-header-quote__value',

                    # Generic selectors
                    'span.tv-symbol-price-quote__value',
                    '.js-symbol-last',
                    '.price-ohlc__last-value'
                ]

                price = None
                for selector in selectors:
                    try:
                        # Try to find the element with a short timeout
                        price_element = WebDriverWait(self.driver, 2).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                        price_text = price_element.text.strip()

                        # Clean the price: remove any non-numeric characters except for dots
                        price_text = ''.join(c for c in price_text if c.isdigit() or c == '.')

                        # Make sure we have a valid price
                        if price_text:
                            price = float(price_text)
                            logger.info(f"Found price for {symbol} using selector {selector}: {price}")
                            break
                    except Exception as e:
                        logger.debug(f"Selector {selector} failed: {str(e)}")
                        continue

            # If price still not found, check the page title
            if price is None:
                try:
                    title = self.driver.title
                    import re
                    price_match = re.search(r'(\d+\.\d+)', title)
                    if price_match:
                        price = float(price_match.group(1))
                        logger.info(f"Extracted price from title: {price}")
                except Exception as e:
                    logger.debug(f"Title extraction failed: {str(e)}")

            # If still no price found, generate a sample price
            if price is None:
                logger.warning(f"Could not find price element for {symbol} using Selenium. Generating sample price.")
                return self._generate_sample_price(symbol)

            # Get current timestamp
            timestamp = datetime.now()

            return {
                'Symbol': symbol,
                'Date': timestamp,
                'Close': price,
                # We don't have OHLV data from this simple scrape, so we'll use Close for all
                'Open': price,
                'High': price,
                'Low': price,
                'Volume': 0  # We don't have volume data
            }

        except Exception as e:
            logger.error(f"Error scraping TradingView with Selenium for {symbol}: {str(e)}")
            # Always fall back to sample data on error
            return self._generate_sample_price(symbol)

    def get_tradingview_price(self, symbol):
        """
        Get price from TradingView

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Dictionary with price data
        """
        try:
            if not SELENIUM_AVAILABLE or self.driver is None:
                logger.warning(f"Selenium not available for {symbol}. Using sample price.")
                return self._generate_sample_price(symbol)

            if self.use_real_time and self.is_logged_in:
                # Use the direct approach for real-time data if logged in
                logger.info(f"Attempting to get real-time price for {symbol} (logged in: {self.is_logged_in})")
                result = self._get_tradingview_price_realtime(symbol)

                if result:
                    result['IsRealTime'] = True
                    logger.info(f"Successfully retrieved real-time price for {symbol}")
                    return result

                # If real-time fails, try the delayed method
                logger.warning(f"Failed to get real-time price for {symbol}. Falling back to delayed data.")

            # Use the public data method if not logged in or real-time failed
            logger.info(f"Getting delayed price for {symbol} (not logged in or real-time failed)")
            result = self._get_tradingview_price_selenium(symbol)

            if result:
                # Mark as delayed data
                result['IsRealTime'] = False
                logger.info(f"Successfully retrieved delayed price for {symbol}")
                return result

            # If all methods fail, use sample data
            logger.warning(f"Failed to get price for {symbol} from any TradingView method. Using sample price.")
            return self._generate_sample_price(symbol)

        except Exception as e:
            logger.error(f"Error scraping TradingView for {symbol}: {str(e)}")
            return self._generate_sample_price(symbol)

    def _get_tradingview_price_realtime(self, symbol):
        """
        Get real-time price from TradingView when logged in

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Price data with real-time flag
        """
        if not SELENIUM_AVAILABLE or not self.driver:
            logger.warning("Selenium not available or driver not initialized")
            return None

        try:
            # Make sure we're logged in before attempting to get real-time data
            if not self.is_logged_in:
                success = self.login_to_tradingview()
                if not success:
                    logger.warning(f"Failed to log in for real-time {symbol} data")
                    return None

            # Navigate to chart page with the symbol
            # Format the symbol correctly for TradingView
            if ':' in symbol:
                # Symbol already has exchange prefix
                formatted_symbol = symbol
            else:
                # Assume EGX exchange if not specified
                formatted_symbol = f"EGX:{symbol}"

            # Make sure we're on a fresh chart page
            self.driver.get("https://www.tradingview.com/chart/")
            time.sleep(3)

            # Clear any existing symbol and search for the new one
            try:
                logger.info(f"Setting up chart for {formatted_symbol}")

                # Find and click the symbol search box
                search_selectors = [
                    "[data-name='symbol-search-open-button']",
                    ".toggleButton-iqKo8bBn",
                    ".js-symbol-search-open-button",
                    ".input-tzZJ85Sd"  # Newer versions
                ]

                search_box = None
                for selector in search_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            search_box = elements[0]
                            logger.info(f"Found search box using selector: {selector}")
                            break
                    except:
                        continue

                if not search_box:
                    # Try an alternative approach - look for anything clickable with "symbol" in the class
                    search_box = self.driver.find_element(By.CSS_SELECTOR, "[class*='symbol'][class*='button'], [class*='Symbol'][class*='button']")

                if search_box:
                    # Click to open search
                    search_box.click()
                    time.sleep(1)

                    # Now find the input field that appears
                    input_selectors = [
                        ".search-ZXzPWlgq input",
                        "input.search-Hsmn_0WX",
                        "input[placeholder*='Symbol']",
                        "input[placeholder*='symbol']"
                    ]

                    input_field = None
                    for selector in input_selectors:
                        try:
                            elements = WebDriverWait(self.driver, 3).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                            )
                            if elements:
                                input_field = elements
                                logger.info(f"Found input field using selector: {selector}")
                                break
                        except:
                            continue

                    if not input_field:
                        # Try XPath as fallback
                        try:
                            input_field = WebDriverWait(self.driver, 3).until(
                                EC.presence_of_element_located((By.XPATH, "//input[@type='text' and @placeholder]"))
                            )
                        except:
                            logger.warning("Could not find symbol search input field")

                    # If we found the input, enter the symbol
                    if input_field:
                        input_field.clear()
                        input_field.send_keys(formatted_symbol)
                        time.sleep(1)
                        input_field.send_keys(Keys.RETURN)
                        time.sleep(3)  # Allow chart to load
                    else:
                        # Direct URL navigation as fallback
                        logger.info(f"Using direct URL navigation to symbol: {formatted_symbol}")

                        # Format the URL correctly - TradingView chart URLs use exchange:symbol format
                        if ':' in formatted_symbol:
                            # Already has the correct format
                            self.driver.get(f"https://www.tradingview.com/chart/?symbol={formatted_symbol}")
                        else:
                            # Need to add exchange prefix
                            self.driver.get(f"https://www.tradingview.com/chart/?symbol=EGX:{formatted_symbol}")

                        # Take a screenshot for debugging
                        try:
                            self.driver.save_screenshot(f"{symbol}_chart_page.png")
                            logger.info(f"Saved screenshot of chart page to {symbol}_chart_page.png")
                        except Exception as e:
                            logger.warning(f"Could not save screenshot: {str(e)}")

                        time.sleep(5)  # Allow chart to load

                # Check if we successfully loaded the symbol
                page_source = self.driver.page_source
                symbol_upper = symbol.upper()

                if symbol_upper not in page_source and formatted_symbol not in page_source:
                    logger.warning(f"Symbol {symbol} might not have loaded correctly")

                # Now look for price data on the chart page
                price_selectors = [
                    ".lastValueValue-QCJHGtef", # Main chart price value
                    ".valueValue-QCJHGtef",
                    ".tv-symbol-price-quote__value",
                    ".price-BWzbHq3E",
                    ".price-Fs2_L5hV"  # Newer versions
                ]

                for selector in price_selectors:
                    try:
                        element = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )

                        if element:
                            price_text = element.text.strip()
                            logger.info(f"Found price {price_text} using selector: {selector}")

                            # Clean up the price text and convert to float
                            # Remove any currency symbols or commas
                            price_text = price_text.replace(',', '').replace('$', '').replace('€', '')
                            price = float(price_text)

                            # Check if we have real-time data
                            is_real_time = self._is_real_time_data()
                            real_time_status = "REAL-TIME" if is_real_time else "DELAYED"
                            logger.info(f"Retrieved {real_time_status} price for {symbol}: {price}")

                            # Get current timestamp
                            timestamp = datetime.now()

                            # Get more OHLC data if possible
                            ohlc_data = self._try_get_ohlc_data(symbol)

                            result = {
                                'Symbol': symbol,
                                'Price': price,
                                'Date': timestamp,
                                'IsRealTime': is_real_time,
                                'Source': 'TradingView Premium' if is_real_time else 'TradingView Delayed'
                            }

                            # If we have OHLC data, include it
                            if ohlc_data:
                                result.update(ohlc_data)
                            else:
                                # Use the current price for all OHLC values
                                result.update({
                                    'Open': price,
                                    'High': price,
                                    'Low': price,
                                    'Close': price,
                                    'Volume': 0
                                })

                            return result
                    except Exception as e:
                        logger.warning(f"Error with price selector {selector}: {str(e)}")
                        continue

                # If we couldn't find the price with selectors, try JavaScript
                try:
                    logger.info("Trying to extract price using JavaScript")
                    price_js = self.driver.execute_script("""
                        // Try to get the last price from various possible locations in the DOM
                        // Updated selectors based on the latest TradingView UI (2023-2024)
                        const selectors = [
                            // New TradingView selectors (2023-2024)
                            '.last-JWXMzmRr',
                            '.last-JWXMzmRr.js-symbol-last',
                            '.last-zoF9r75I.js-symbol-last',
                            '.lastValue-dxGZjzSC',
                            '.lastValueValue-dxGZjzSC',

                            // Legacy selectors
                            '.lastValueValue-QCJHGtef',
                            '.valueValue-QCJHGtef',
                            '.tv-symbol-price-quote__value',
                            '.price-BWzbHq3E',
                            '[data-qa-label="price"]',

                            // Chart page selectors
                            '.chart-markup-table.pane-legend-line.main > span:nth-child(2)',
                            '.chart-markup-table.pane-legend-line.main .pane-legend-line__value'
                        ];

                        // Try each selector
                        for (const selector of selectors) {
                            const elements = document.querySelectorAll(selector);
                            if (elements.length > 0) {
                                const priceText = elements[0].textContent.trim()
                                    .replace(',', '')
                                    .replace('$', '')
                                    .replace('€', '')
                                    .replace('EGP', '');
                                console.log('Found price with selector: ' + selector + ' = ' + priceText);
                                return priceText;
                            }
                        }

                        // Try to get it from TradingView's internal data structures
                        if (window.TradingView && window.TradingView.ActiveChart && window.TradingView.ActiveChart.symbolInfo) {
                            // This checks internal subscription info which indicates if we have real-time data
                            console.log('Found price in TradingView.ActiveChart.symbolInfo: ' + window.TradingView.ActiveChart.symbolInfo.last);
                            return window.TradingView.ActiveChart.symbolInfo.last;
                        }

                        // Try alternative internal data structures
                        if (window.ChartApiInstance && window.ChartApiInstance.activeChart && window.ChartApiInstance.activeChart.symbolInfo) {
                            console.log('Found price in ChartApiInstance: ' + window.ChartApiInstance.activeChart.symbolInfo.last);
                            return window.ChartApiInstance.activeChart.symbolInfo.last;
                        }

                        // Try to find price in any element with a numeric value that might be the price
                        const allElements = document.querySelectorAll('span, div');
                        const priceRegex = /^[0-9]+\.[0-9]+$/;
                        for (const el of allElements) {
                            const text = el.textContent.trim();
                            if (priceRegex.test(text) && text.length < 10) {
                                console.log('Found potential price via regex: ' + text);
                                return text;
                            }
                        }

                        return null;
                    """)

                    if price_js and price_js not in ['null', 'undefined', None]:
                        try:
                            price = float(price_js)
                            logger.info(f"Found price via JavaScript: {price}")

                            # Check if we have real-time data
                            is_real_time = self._is_real_time_data()

                            # Get current timestamp
                            timestamp = datetime.now()

                            return {
                                'Symbol': symbol,
                                'Price': price,
                                'Date': timestamp,
                                'Open': price,
                                'High': price,
                                'Low': price,
                                'Close': price,
                                'Volume': 0,
                                'IsRealTime': is_real_time,
                                'Source': 'TradingView Premium' if is_real_time else 'TradingView Delayed'
                            }
                        except:
                            logger.warning(f"Invalid price from JavaScript: {price_js}")
                except Exception as e:
                    logger.error(f"Error extracting price with JavaScript: {str(e)}")

            except Exception as e:
                logger.error(f"Error setting up chart for {symbol}: {str(e)}")

            # If we get here, we couldn't get the real-time price
            logger.warning(f"Could not get real-time price for {symbol}")
            return None

        except Exception as e:
            logger.error(f"Error getting real-time price for {symbol}: {str(e)}")
            return None

    def _is_real_time_data(self):
        """
        Check if the current chart is showing real-time data

        Returns:
            bool: True if real-time data, False if delayed
        """
        try:
            # Different ways to check if we have real-time data

            # 1. Look for UI indicators (updated for latest TradingView UI)
            realtime_indicators = [
                # New TradingView selectors (2023-2024)
                ".js-data-mode-real-time",
                ".tv-data-mode--realtime",
                ".data-mode-rtd",
                ".data-mode--realtime",
                ".js-real-time-badge",
                ".real-time-badge",

                # Legacy selectors
                "span.pro-mark",
                ".js-pro-badge",
                "[data-qty-label='real-time']",
                ".paid-feature-badge",
                ".badge-TRXNFRrd"
            ]

            for selector in realtime_indicators:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    logger.info(f"Found real-time indicator: {selector}")
                    return True

            # 2. Check for delayed data indicators (updated for latest TradingView UI)
            delayed_indicators = [
                # New TradingView selectors (2023-2024)
                ".js-data-mode-delayed",
                ".tv-data-mode--delayed",
                ".data-mode-delayed",
                ".data-mode--delayed",

                # Legacy selectors
                ".js-delayed-data-badge",
                "span.delayed-data-badge",
                "[data-qty-label='delayed']"
            ]

            for selector in delayed_indicators:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    logger.info(f"Found delayed data indicator: {selector}")
                    return False

            # 3. Use JavaScript to check (enhanced with more detection methods)
            is_realtime = self.driver.execute_script("""
                // Take a screenshot of the DOM for debugging
                console.log('Checking for real-time data indicators...');

                // Check for real-time indicators (updated for latest TradingView UI)
                const realtimeSelectors = [
                    '.js-data-mode-real-time',
                    '.tv-data-mode--realtime',
                    '.data-mode-rtd',
                    '.data-mode--realtime',
                    '.js-real-time-badge',
                    '.real-time-badge',
                    '.pro-mark',
                    '.js-pro-badge',
                    '[data-qty-label="real-time"]',
                    '.paid-feature-badge'
                ];

                for (const selector of realtimeSelectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        console.log('Found real-time indicator: ' + selector);
                        return true;
                    }
                }

                // Check for delayed indicators (updated for latest TradingView UI)
                const delayedSelectors = [
                    '.js-data-mode-delayed',
                    '.tv-data-mode--delayed',
                    '.data-mode-delayed',
                    '.data-mode--delayed',
                    '.js-delayed-data-badge',
                    '[data-qty-label="delayed"]'
                ];

                for (const selector of delayedSelectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        console.log('Found delayed indicator: ' + selector);
                        return false;
                    }
                }

                // Check for text content indicating real-time or delayed data
                const allElements = document.querySelectorAll('span, div');
                for (const el of allElements) {
                    const text = el.textContent.trim().toLowerCase();
                    if (text === 'real-time' || text === 'realtime' || text === 'real time') {
                        console.log('Found real-time text indicator');
                        return true;
                    }
                    if (text === 'delayed' || text.includes('min delay') || text.includes('minute delay')) {
                        console.log('Found delayed text indicator');
                        return false;
                    }
                }

                // Check TradingView data structures
                if (window.TradingView && window.TradingView.ChartApiInstance &&
                    window.TradingView.ChartApiInstance.subscriptionInfo) {
                    // This checks internal subscription info which indicates if we have real-time data
                    const isRealTime = window.TradingView.ChartApiInstance.subscriptionInfo.type === 'real-time';
                    console.log('ChartApiInstance subscription type: ' + window.TradingView.ChartApiInstance.subscriptionInfo.type);
                    return isRealTime;
                }

                // Check for pro account indicators
                const hasProBadge = document.querySelector('.tv-header__user-menu-button--pro, .js-user-pro-badge, [data-role="user-pro-badge"]');
                if (hasProBadge) {
                    console.log('Found pro account badge, assuming real-time data');
                    return true;
                }

                // Default - if we're logged in with a paid account, assume real-time
                const isLoggedIn = Boolean(
                    (window.user && window.user.username) ||
                    (window.TradingView && window.TradingView.User && window.TradingView.User.username)
                );

                if (isLoggedIn) {
                    console.log('User is logged in, assuming real-time data');
                    return true;
                }

                console.log('Could not determine real-time status, assuming delayed');
                return false;
            """)

            if is_realtime:
                logger.info("Detected real-time data via JavaScript")
                return True

            # 4. Check if we have a paid account as a fallback
            # If we're logged in with a paid account, we likely have real-time data
            pro_indicators = self.driver.find_elements(By.CSS_SELECTOR,
                ".tv-header__user-menu-button--pro, .js-user-pro-badge, [data-role='user-pro-badge']")

            if pro_indicators:
                logger.info("User has pro/paid account, assuming real-time data")
                return True

            # If we can't determine, assume it's delayed
            return False

        except Exception as e:
            logger.error(f"Error checking if data is real-time: {str(e)}")
            return False

    def _extract_price_from_header(self, symbol):
        """
        Extract price from the TradingView header/title area

        This method specifically targets the main price display in the header
        section of TradingView as seen in the user's screenshot.

        Args:
            symbol (str): Stock symbol

        Returns:
            float: Price if found, None otherwise
        """
        try:
            # Take a screenshot for debugging
            try:
                self.driver.save_screenshot(f"{symbol}_price_extraction.png")
                logger.info(f"Saved screenshot to {symbol}_price_extraction.png")
            except Exception as e:
                logger.warning(f"Could not save screenshot: {str(e)}")

            # Try to extract the price from the header area
            price_js = self.driver.execute_script("""
                // This targets the main price display in the header
                // Based on the user's screenshot showing "80.660 EGP"
                console.log('Attempting to extract price from header for ' + arguments[0]);

                // First try the exact selectors from the user's screenshot
                // The price appears as "80.660" in the main header
                const exactSelectors = [
                    '.last-JWXMzmRr',  // This is the selector visible in the screenshot
                    '.last-JWXMzmRr.js-symbol-last',
                    '.tv-symbol-header-quote__value',
                    '.tv-symbol-price-quote__last-value'
                ];

                for (const selector of exactSelectors) {
                    const el = document.querySelector(selector);
                    if (el && el.textContent) {
                        const text = el.textContent.trim();
                        console.log('Found exact price element with selector ' + selector + ': ' + text);
                        // Extract just the number part
                        const match = text.match(/([0-9]+[.,][0-9]+)/);
                        if (match) {
                            return match[1].replace(',', '.');
                        }
                    }
                }

                // Try the main header price element
                const headerPriceElements = document.querySelectorAll('.tv-symbol-header__first-line, .tv-symbol-price-quote__last-value');
                for (const el of headerPriceElements) {
                    if (el && el.textContent) {
                        const text = el.textContent.trim();
                        console.log('Found header price element: ' + text);
                        // Extract just the number part
                        const match = text.match(/([0-9]+[.,][0-9]+)/);
                        if (match) {
                            return match[1].replace(',', '.');
                        }
                    }
                }

                // Try the large price display in the chart view
                const largePrice = document.querySelector('.chart-status-wrapper .price-qQzSCWsb, .chart-status-wrapper .last-JWXMzmRr');
                if (largePrice) {
                    const text = largePrice.textContent.trim();
                    console.log('Found large price element: ' + text);
                    // Extract just the number part
                    const match = text.match(/([0-9]+[.,][0-9]+)/);
                    if (match) {
                        return match[1].replace(',', '.');
                    }
                }

                // Try to find any element that contains both the symbol and a price
                const symbolElements = document.querySelectorAll('h1, h2, h3, div.title');
                const priceRegex = /([0-9]+[.,][0-9]+)/;

                for (const el of symbolElements) {
                    const text = el.textContent.trim();
                    if (text.includes(arguments[0]) || text.includes('EGP')) {
                        const match = text.match(priceRegex);
                        if (match) {
                            console.log('Found price in symbol element: ' + match[1]);
                            return match[1].replace(',', '.');
                        }
                    }
                }

                // Last resort: look for any element with a number that looks like a price
                const allElements = document.querySelectorAll('span, div');
                for (const el of allElements) {
                    const text = el.textContent.trim();
                    // Look for numbers like 80.660
                    if (/^[0-9]+\.[0-9]+$/.test(text)) {
                        console.log('Found potential price via text content: ' + text);
                        return text;
                    }
                }

                return null;
            """, symbol)

            if price_js and price_js not in ['null', 'undefined', None]:
                try:
                    price = float(price_js)
                    logger.info(f"Successfully extracted price from header: {price}")
                    return price
                except ValueError:
                    logger.warning(f"Invalid price extracted from header: {price_js}")

            return None

        except Exception as e:
            logger.error(f"Error extracting price from header: {str(e)}")
            return None

    def _try_get_ohlc_data(self, symbol):
        """
        Try to extract OHLC data from TradingView chart

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: OHLC data if available, None otherwise
        """
        try:
            # Use JavaScript to extract data from TradingView
            ohlc_data = self.driver.execute_script("""
                try {
                    // Try to get data from TradingView's internal structures
                    if (window.TradingView && window.TradingView.ActiveChart) {
                        const chartData = window.TradingView.ActiveChart.studyMetaInfo;
                        const symbolInfo = window.TradingView.ActiveChart.symbolInfo;

                        if (symbolInfo) {
                            return {
                                open: symbolInfo.open,
                                high: symbolInfo.high,
                                low: symbolInfo.low,
                                close: symbolInfo.last || symbolInfo.close,
                                volume: symbolInfo.volume
                            };
                        }
                    }

                    // Alternative method - look for values in the DOM
                    const indicators = document.querySelectorAll('.valueValue-QCJHGtef, .value-DWZXOdoK');
                    if (indicators.length >= 4) {
                        // Typically these are in OHLC order
                        return {
                            open: parseFloat(indicators[0].textContent.replace(',', '')),
                            high: parseFloat(indicators[1].textContent.replace(',', '')),
                            low: parseFloat(indicators[2].textContent.replace(',', '')),
                            close: parseFloat(indicators[3].textContent.replace(',', '')),
                            volume: indicators.length >= 5 ? parseFloat(indicators[4].textContent.replace(',', '')) : 0
                        };
                    }
                } catch (e) {
                    console.error('Error extracting OHLC data:', e);
                }
                return null;
            """)

            if ohlc_data and isinstance(ohlc_data, dict):
                return {
                    'Open': ohlc_data.get('open', 0),
                    'High': ohlc_data.get('high', 0),
                    'Low': ohlc_data.get('low', 0),
                    'Close': ohlc_data.get('close', 0),
                    'Volume': ohlc_data.get('volume', 0)
                }

            return None

        except Exception as e:
            logger.error(f"Error extracting OHLC data: {str(e)}")
            return None

    def get_mubasher_price(self, symbol):
        """
        Scrape price from Mubasher

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Dictionary with price data
        """
        # First try with Selenium if available
        if SELENIUM_AVAILABLE and self.driver is not None:
            try:
                return self._get_mubasher_price_selenium(symbol)
            except Exception as e:
                logger.error(f"Selenium scraping failed for Mubasher {symbol}: {str(e)}")
                logger.info("Falling back to requests method")

        # Fall back to requests method
        return self._get_mubasher_price_requests(symbol)

    def _get_mubasher_price_selenium(self, symbol):
        """
        Scrape price from Mubasher using Selenium

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Dictionary with price data
        """
        if self.driver is None:
            logger.warning("Selenium driver not initialized. Falling back to sample data.")
            return self._generate_sample_price(symbol)

        try:
            # Mubasher URL for EGX stocks
            url = f"https://english.mubasher.info/markets/EGX/stocks/{symbol}"

            # Define a function for navigating to the page that can be retried
            def navigate_to_page():
                logger.info(f"Navigating to {url}")
                self.driver.get(url)

                # Wait for the page to load (up to 5 seconds)
                time.sleep(5)
                return True

            try:
                # Use retry mechanism if available
                if RETRY_AVAILABLE:
                    try:
                        retry_with_backoff(
                            max_retries=3,
                            backoff_factor=2.0,
                            initial_wait=1.0,
                            max_wait=15.0,
                            jitter=True,
                            exceptions=(TimeoutException, WebDriverException)
                        )(navigate_to_page)()
                    except Exception as e:
                        logger.error(f"Error navigating to {url} after retries: {str(e)}")
                        return self._generate_sample_price(symbol)
                else:
                    # Fallback to non-retry approach
                    navigate_to_page()
            except Exception as e:
                logger.error(f"Error navigating to {url}: {str(e)}")
                return self._generate_sample_price(symbol)

            # Try multiple selectors to find the price
            selectors = [
                'div.stock-price',
                '.stock-price',
                '.stock-details__price',
                '.stock-details__last-price'
            ]

            price = None
            for selector in selectors:
                try:
                    # Try to find the element with a short timeout
                    price_element = WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    price_text = price_element.text.strip().replace(',', '')

                    # Clean the price: remove any non-numeric characters except for dots
                    price_text = ''.join(c for c in price_text if c.isdigit() or c == '.')

                    # Make sure we have a valid price
                    if price_text:
                        price = float(price_text)
                        logger.info(f"Found Mubasher price for {symbol} using selector {selector}: {price}")
                        break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {str(e)}")
                    continue

            # If still no price found, generate a sample price
            if price is None:
                logger.warning(f"Could not find Mubasher price element for {symbol} using Selenium. Generating sample price.")
                return self._generate_sample_price(symbol)

            # Get current timestamp
            timestamp = datetime.now()

            return {
                'Symbol': symbol,
                'Date': timestamp,
                'Close': price,
                # We don't have OHLV data from this simple scrape, so we'll use Close for all
                'Open': price,
                'High': price,
                'Low': price,
                'Volume': 0,  # We don't have volume data
            }

        except Exception as e:
            logger.error(f"Error scraping Mubasher with Selenium for {symbol}: {str(e)}")
            # Always fall back to sample data on error
            return self._generate_sample_price(symbol)

    def _get_mubasher_price_requests(self, symbol):
        """
        Scrape price from Mubasher using requests

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Dictionary with price data
        """
        try:
            # Mubasher URL for EGX stocks
            url = f"https://english.mubasher.info/markets/EGX/stocks/{symbol}"

            # Define a function for making the request that can be retried
            def make_request():
                # Send request
                response = requests.get(url, headers=self.headers)
                response.raise_for_status()
                return response

            # Use retry mechanism if available
            if RETRY_AVAILABLE:
                try:
                    response = retry_with_backoff(
                        max_retries=3,
                        backoff_factor=2.0,
                        initial_wait=1.0,
                        max_wait=15.0,
                        jitter=True,
                        exceptions=(requests.RequestException,)
                    )(make_request)()
                except Exception as e:
                    logger.error(f"Error making request to {url} after retries: {str(e)}")
                    return self._generate_sample_price(symbol)
            else:
                # Fallback to non-retry approach
                response = make_request()

            # Parse HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract price data
            price_element = soup.select_one('div.stock-price')

            if not price_element:
                logger.error(f"Could not find Mubasher price element for {symbol}")
                return self._generate_sample_price(symbol)

            price = float(price_element.text.strip().replace(',', ''))

            # Get current timestamp
            timestamp = datetime.now()

            return {
                'Symbol': symbol,
                'Date': timestamp,
                'Close': price,
                # We don't have OHLV data from this simple scrape, so we'll use Close for all
                'Open': price,
                'High': price,
                'Low': price,
                'Volume': 0  # We don't have volume data
            }

        except Exception as e:
            logger.error(f"Error scraping Mubasher for {symbol}: {str(e)}")
            return self._generate_sample_price(symbol)

    def get_price(self, symbol, use_cache=True):
        """
        Get stock price from the selected source

        Args:
            symbol (str): Stock symbol
            use_cache (bool): Whether to use cached data

        Returns:
            dict: Dictionary with price data
        """
        # Check if we need to initialize Selenium
        if SELENIUM_AVAILABLE and self.driver is None:
            if ERROR_HANDLING_AVAILABLE:
                with ErrorHandler(log_level=logging.WARNING) as handler:
                    self.initialize_driver()
                    # If real-time is requested, try to log in
                    if self.use_real_time and self.tradingview_email and self.tradingview_password:
                        if self.is_gmail:
                            logger.info("Using Google authentication flow for TradingView")
                            self.login_to_tradingview_via_google()
                        else:
                            logger.info("Using standard authentication flow for TradingView")
                            self.login_to_tradingview()

                # If there was an exception, log it but continue
                if handler.exception:
                    logger.error(f"Failed to initialize Selenium: {str(handler.exception)}")
                    # Continue, will fall back to sample data
            else:
                # Fallback to basic error handling
                try:
                    self.initialize_driver()
                    # If real-time is requested, try to log in
                    if self.use_real_time and self.tradingview_email and self.tradingview_password:
                        if self.is_gmail:
                            logger.info("Using Google authentication flow for TradingView")
                            self.login_to_tradingview_via_google()
                        else:
                            logger.info("Using standard authentication flow for TradingView")
                            self.login_to_tradingview()
                except Exception as e:
                    logger.error(f"Failed to initialize Selenium: {str(e)}")
                    # Continue, will fall back to sample data

        # Define the actual price fetching function to be used with retry
        def fetch_price():
            if self.source.lower() == "tradingview":
                logger.info(f"Getting price for {symbol} from tradingview")
                price_data = self.get_tradingview_price(symbol)
            elif self.source.lower() == "mubasher":
                logger.info(f"Getting price for {symbol} from mubasher")
                price_data = self.get_mubasher_price(symbol)
            else:
                error_msg = f"Unknown source: {self.source}"
                if ERROR_HANDLING_AVAILABLE:
                    raise ConfigError(error_msg, details={"source": self.source, "symbol": symbol})
                else:
                    logger.error(error_msg)
                    return self._generate_sample_price(symbol)

            if price_data:
                logger.info(f"Successfully got price for {symbol} from {self.source}")
                return price_data
            else:
                logger.warning(f"Failed to get price for {symbol} from {self.source}")
                return self._generate_sample_price(symbol)

        # Use enhanced error handling if available
        if ERROR_HANDLING_AVAILABLE:
            # Use the robust_function decorator
            decorated_fetch = robust_function(
                max_retries=3,
                retry_delay=1.0,
                backoff_factor=2.0,
                fallback_return=self._generate_sample_price(symbol),
                exceptions_to_retry=(
                    TimeoutException,
                    WebDriverException,
                    NoSuchElementException,
                    StaleElementReferenceException,
                    requests.RequestException,
                    ScrapingError
                ),
                log_level=logging.WARNING,
                log_execution=True
            )(fetch_price)

            return decorated_fetch()
        # Use retry mechanism if available
        elif RETRY_AVAILABLE:
            try:
                # Define a callback for retry events
                def on_retry(exception, retry_count, wait_time):
                    logger.warning(f"Retry {retry_count} for {symbol} after error: {str(exception)}. Waiting {wait_time:.2f}s")

                # Use the retry decorator with our fetch_price function
                price_data = retry_with_backoff(
                    max_retries=3,
                    backoff_factor=2.0,
                    initial_wait=1.0,
                    max_wait=30.0,
                    jitter=True,
                    exceptions=(
                        TimeoutException,
                        WebDriverException,
                        NoSuchElementException,
                        StaleElementReferenceException,
                        requests.RequestException
                    ),
                    on_retry=on_retry
                )(fetch_price)()

                return price_data
            except Exception as e:
                logger.error(f"Error getting price for {symbol} after retries: {str(e)}")
                return self._generate_sample_price(symbol)
        else:
            # Fallback to basic error handling
            try:
                return fetch_price()
            except Exception as e:
                logger.error(f"Error getting price for {symbol}: {str(e)}")
                return self._generate_sample_price(symbol)

    def get_prices_dataframe(self, symbols, interval=60, num_samples=1):
        """
        Get prices for multiple symbols and return as DataFrame

        Args:
            symbols (list): List of stock symbols
            interval (int): Interval between requests in seconds
            num_samples (int): Number of samples to collect

        Returns:
            pd.DataFrame: DataFrame with price data
        """
        all_data = []

        for _ in range(num_samples):
            for symbol in symbols:
                price_data = self.get_price(symbol)

                if price_data:
                    all_data.append(price_data)

                # Add random delay to avoid being blocked
                time.sleep(interval + random.uniform(0, 2))

        if not all_data:
            logger.error("No price data collected")
            return None

        return pd.DataFrame(all_data)

    def get_tradingview_widget_price(self, symbol):
        """
        Get price directly from TradingView widget without using Selenium
        This is a lightweight method that can be used with the TradingView widget integration

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Dictionary with price data including Date, Open, High, Low, Close, Volume
        """
        try:
            # Format the symbol correctly for TradingView
            if ':' in symbol:
                # Symbol already has exchange prefix
                formatted_symbol = symbol
            else:
                # Assume EGX exchange if not specified
                formatted_symbol = f"EGX:{symbol}"

            # Get current time
            current_time = datetime.now()

            # Create a basic price data structure
            price_data = {
                'Date': current_time,
                'Symbol': symbol,
                'Exchange': formatted_symbol.split(':')[0],
                'Close': None,  # Will be populated by the TradingView widget
                'Open': None,
                'High': None,
                'Low': None,
                'Volume': None,
                'Source': 'TradingView Widget',
                'Delayed': True,  # TradingView free data is delayed by ~15 minutes
                'Last_Update': current_time.strftime('%Y-%m-%d %H:%M:%S')
            }

            # Note: The actual price will be populated by the TradingView widget in JavaScript
            # This method just creates the data structure to be used with the widget

            logger.info(f"Created TradingView widget price data structure for {formatted_symbol}")
            return price_data

        except Exception as e:
            logger.error(f"Error creating TradingView widget price data for {symbol}: {str(e)}")
            return None

    def _is_market_open(self, symbol=None):
        """
        Check if the market is currently open for the given symbol.

        For EGX, trading hours are:
        - Sunday to Thursday: 10:00 AM to 2:30 PM (Egypt time, GMT+2)
        - Friday and Saturday: Closed

        Args:
            symbol (str, optional): Stock symbol. Defaults to None.

        Returns:
            bool: True if market is open, False otherwise
        """
        # Get current time in Egypt timezone (GMT+2)
        try:
            # Try to use pytz if available
            import pytz
            egypt_tz = pytz.timezone('Africa/Cairo')
            now = datetime.now(egypt_tz)
        except ImportError:
            # Fallback to manual offset
            now = datetime.now()
            # Add 2 hours to UTC time to get Egypt time
            # This is a simplification and doesn't account for DST
            now = now.replace(hour=now.hour + 2)

        # Check if it's a weekend (Friday or Saturday)
        if now.weekday() >= 4:  # 4 is Friday, 5 is Saturday
            return False

        # Check if it's within trading hours (10:00 AM to 2:30 PM)
        market_open = now.replace(hour=10, minute=0, second=0, microsecond=0)
        market_close = now.replace(hour=14, minute=30, second=0, microsecond=0)

        return market_open <= now <= market_close

    def _generate_sample_price(self, symbol):
        """
        Generate a sample price when scraping fails

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Dictionary with sample price data
        """
        logger.warning(f"Generating sample price for {symbol}")

        # Try to get historical data for more realistic prices
        try:
            import os
            import pandas as pd

            # Check if we have historical data for this stock
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data/stocks')
            file_path = os.path.join(data_dir, f"{symbol}.csv")

            if os.path.exists(file_path):
                # Read the last few rows to get recent prices
                df = pd.read_csv(file_path)
                if 'Close' in df.columns and len(df) > 0:
                    # Use the last close price with small random variation
                    last_price = df['Close'].iloc[-1]
                    # Add a small random variation to make it look realistic
                    variation = random.uniform(-0.005, 0.005)  # ±0.5%
                    price = round(last_price * (1 + variation), 2)
                    logger.info(f"Generated sample price based on historical data: {price}")

                    # Get current timestamp
                    timestamp = datetime.now()

                    return {
                        'Symbol': symbol,
                        'Date': timestamp,
                        'Close': price,
                        'Open': price,
                        'High': price,
                        'Low': price,
                        'Volume': 0,
                        'IsRealTime': False,
                        'IsMarketOpen': self._is_market_open(symbol),
                        'Note': 'Sample data based on historical prices',
                        'Source': 'Historical Data'
                    }
        except Exception as e:
            logger.debug(f"Error generating price from historical data: {str(e)}")

        # If we couldn't get historical data, use predefined values
        # Use a realistic price based on the stock
        if symbol == 'COMI':
            price = 79.65
        elif symbol == 'ABUK':
            price = 42.50
        elif symbol == 'SWDY':
            price = 15.75
        elif symbol == 'EFIC':
            price = 30.20
        elif symbol == 'JUFO':
            price = 10.85
        else:
            # Default price with small random variation
            price = round(random.uniform(40, 80), 2)

        # Add a small random variation to make it look realistic
        variation = random.uniform(-0.01, 0.01)  # ±1%
        price = round(price * (1 + variation), 2)

        # Get current timestamp
        timestamp = datetime.now()

        logger.info(f"Generated sample price for {symbol}: {price}")

        return {
            'Symbol': symbol,
            'Date': timestamp,
            'Close': price,
            'Open': price,
            'High': price,
            'Low': price,
            'Volume': 0,
            'IsRealTime': False,
            'IsMarketOpen': False,  # Conservative assumption
            'Note': 'Sample data (predefined values)',
            'Source': 'Predefined'
        }

# Example usage
if __name__ == "__main__":
    # For delayed data (15-minute delay)
    scraper = PriceScraper(source='tradingview')
    delayed_price = scraper.get_price('COMI')  # Commercial International Bank
    print("Delayed price:", delayed_price)

# For real-time data using paid account

# Example usage
if __name__ == "__main__":
    # Replace with your actual Trading View credentials
    realtime_scraper = PriceScraper(
        source='tradingview',
        use_real_time=True,
        username='your_tradingview_email',
        password='your_tradingview_password'
    )
    realtime_price = realtime_scraper.get_price('COMI')  # Commercial International Bank
    print("Real-time price:", realtime_price)
